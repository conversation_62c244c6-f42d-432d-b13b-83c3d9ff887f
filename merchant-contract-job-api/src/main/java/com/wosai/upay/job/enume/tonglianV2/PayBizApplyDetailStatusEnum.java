package com.wosai.upay.job.enume.tonglianV2;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付业务开通申请详细状态枚举
 *
 * <AUTHOR>
@Getter
@AllArgsConstructor
public enum PayBizApplyDetailStatusEnum {

    /**
     * 进件审核中
     */
    CONTRACT_AUDITING(1, "进件审核中", "进件审核中", "进件审核中，等待审核结果"),

    /**
     * 进件审核失败
     */
    CONTRACT_AUDIT_FAILED(2, "进件审核失败", "进件审核失败", "进件审核未通过，请查看审核意见"),

    /**
     * 待法人签约
     */
    WAITING_LEGAL_SIGN(3, "待法人签约", "待法人签约", "待法人签约电子协议"),

    /**
     * 法人签约协议失效
     */
    LEGAL_SIGN_EXPIRED(4, "法人签约协议失效", "法人签约协议失效", "签约协议失效,请重新获取签约协议"),

    /**
     * 法人签约协议失败
     */
    LEGAL_SIGN_FAILED(5, "法人签约协议失败", "法人签约协议失败", "法人签约未完成，请查看签约结果"),

    /**
     * 待结算人签约
     */
    WAITING_SETTLEMENT_SIGN(6, "待结算人签约", "待结算人签约", "待结算人签约电子协议"),

    /**
     * 结算人签约协议失效
     */
    SETTLEMENT_SIGN_EXPIRED(7, "结算人签约协议失效", "结算人签约协议失效", "签约协议失效,请重新获取签约协议"),

    /**
     * 结算人签约失败
     */
    SETTLEMENT_SIGN_FAILED(8, "结算人签约失败", "结算人签约失败", "结算人签约未完成，请查看签约结果"),

    /**
     * 风控审核中
     */
    RISK_CONTROL_AUDITING(9, "风控审核中", "合规补录审核中", "风控审核中，等待审核结果"),

    /**
     * 风控审核驳回
     */
    RISK_CONTROL_REJECTED(10, "风控审核驳回", "合规补录审核驳回", "风控审核未通过，请查看审核意见"),

    /**
     * 待补录
     */
    WAITING_SUPPLEMENT(11, "待补录", "合规补录审核中", "等待补录相关信息"),

    /**
     * 合规补录驳回
     */
    COMPLIANCE_SUPPLEMENT_REJECTED(12, "合规补录驳回", "合规补录审核驳回", "合规补录信息未通过审核，请重新补录"),

    /**
     * 合规补录审核中
     */
    COMPLIANCE_SUPPLEMENT_AUDITING(13, "合规补录审核中", "合规补录审核中", "合规补录信息审核中，等待审核结果"),

    /**
     * 待生效
     */
    WAITING_EFFECTIVE(14, "待生效", "待生效", "合规补录审核通过,是否立即启用通道"),

    /**
     * 启用中
     */
    ENABLING(15, "启用中", "启用中", "启用中,系统正在启用通道,请稍候"),

    /**
     * 启用失败
     */
    ENABLE_FAILED(16, "启用失败", "启用失败", "启用失败"),

    /**
     * 开通成功
     */
    OPEN_SUCCESS(17, "开通成功", "开通成功", "开通成功");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 前端展示文案
     */
    private final String displayText;

    /**
     * 详细描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static PayBizApplyDetailStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PayBizApplyDetailStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为审核中状态（进件审核中或合规补录审核中或风控审核中）
     *
     * @param code 状态码
     * @return true-审核中，false-非审核中
     */
    public static boolean isAuditing(Integer code) {
        return CONTRACT_AUDITING.getCode().equals(code)
            || COMPLIANCE_SUPPLEMENT_AUDITING.getCode().equals(code)
            || RISK_CONTROL_AUDITING.getCode().equals(code);
    }

    /**
     * 判断是否为签约相关状态
     *
     * @param code 状态码
     * @return true-签约相关，false-非签约相关
     */
    public static boolean isSignRelated(Integer code) {
        return WAITING_LEGAL_SIGN.getCode().equals(code) 
            || LEGAL_SIGN_EXPIRED.getCode().equals(code)
            || LEGAL_SIGN_FAILED.getCode().equals(code)
            || WAITING_SETTLEMENT_SIGN.getCode().equals(code)
            || SETTLEMENT_SIGN_EXPIRED.getCode().equals(code)
            || SETTLEMENT_SIGN_FAILED.getCode().equals(code);
    }

    /**
     * 判断是否为补录相关状态
     *
     * @param code 状态码
     * @return true-补录相关，false-非补录相关
     */
    public static boolean isSupplementRelated(Integer code) {
        return WAITING_SUPPLEMENT.getCode().equals(code)
            || COMPLIANCE_SUPPLEMENT_REJECTED.getCode().equals(code)
            || COMPLIANCE_SUPPLEMENT_AUDITING.getCode().equals(code);
    }

    /**
     * 判断是否为失败状态
     *
     * @param code 状态码
     * @return true-失败状态，false-非失败状态
     */
    public static boolean isFailedStatus(Integer code) {
        return CONTRACT_AUDIT_FAILED.getCode().equals(code)
            || LEGAL_SIGN_FAILED.getCode().equals(code)
            || SETTLEMENT_SIGN_FAILED.getCode().equals(code)
            || COMPLIANCE_SUPPLEMENT_REJECTED.getCode().equals(code)
            || RISK_CONTROL_REJECTED.getCode().equals(code);
    }

    /**
     * 判断是否为成功状态
     *
     * @param code 状态码
     * @return true-成功状态，false-非成功状态
     */
    public static boolean isSuccessStatus(Integer code) {
        return OPEN_SUCCESS.getCode().equals(code);
    }

    /**
     * 判断是否为等待状态
     *
     * @param code 状态码
     * @return true-等待状态，false-非等待状态
     */
    public static boolean isWaitingStatus(Integer code) {
        return WAITING_LEGAL_SIGN.getCode().equals(code)
            || WAITING_SETTLEMENT_SIGN.getCode().equals(code)
            || WAITING_SUPPLEMENT.getCode().equals(code)
            || WAITING_EFFECTIVE.getCode().equals(code);
    }

    /**
     * 判断是否为协议失效状态
     *
     * @param code 状态码
     * @return true-协议失效，false-非协议失效
     */
    public static boolean isExpiredStatus(Integer code) {
        return LEGAL_SIGN_EXPIRED.getCode().equals(code)
            || SETTLEMENT_SIGN_EXPIRED.getCode().equals(code);
    }

    /**
     * 判断是否为终态（成功或各种失败状态）
     *
     * @param code 状态码
     * @return true-终态，false-非终态
     */
    public static boolean isFinalState(Integer code) {
        return isSuccessStatus(code) || isFailedStatus(code);
    }
}