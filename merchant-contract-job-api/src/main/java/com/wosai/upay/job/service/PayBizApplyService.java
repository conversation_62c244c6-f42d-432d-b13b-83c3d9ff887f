package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.dto.ProcessRecordInfoDTO;
import com.wosai.upay.job.model.dto.request.PayBizApplyReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizApplyStatusReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizEnabledSetReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizSignResendReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizSignUrlValidateReqDTO;
import com.wosai.upay.job.model.dto.request.ProcessRecordsGetReqDTO;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.model.dto.response.PayBizApplyStatusDetailRspDTO;
import com.wosai.upay.job.model.dto.response.PayBizApplyStatusRspDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * 支付业务开通申请服务接口
 *
 * <AUTHOR>
@JsonRpcService("/rpc/payBiz")
@Validated
public interface PayBizApplyService {

    /**
     * 提交开通通联收银宝
     *
     * @param request 支付业务开通申请请求参数
     * @return 申请结果
     */
    CuaCommonResultDTO applyPayBiz(@Valid PayBizApplyReqDTO request);

    /**
     * 查询支付业务申请状态详情
     *
     * @param request 查询请求
     * @return 状态详情
     */
    PayBizApplyStatusDetailRspDTO getPayBizApplyStatusDetailInfo(@Valid PayBizApplyStatusReqDTO request);

    /**
     * 查询支付业务申请状态
     *
     * @param request 查询请求
     * @return 状态
     */
    PayBizApplyStatusRspDTO getPayBizApplyStatusInfo(@Valid PayBizApplyStatusReqDTO request);

    /**
     * 获取处理记录
     * @param request 查询请求
     * @return 处理记录
     */
    List<ProcessRecordInfoDTO> getProcessRecords(@Valid ProcessRecordsGetReqDTO request);

    /**
     * 设置支付业务是否启用
     * @param request 请求参数
     * @return 结果
     */
    CuaCommonResultDTO setPayBizEnabled(@Valid PayBizEnabledSetReqDTO request);
    
    /**
     * 重新发送签约链接
     * @param request 请求参数
     * @return 结果
     */
    CuaCommonResultDTO reSendSignUrl(@Valid PayBizSignResendReqDTO request);

    /**
     * 校验签约链接是否过期
     * @param request 请求参数
     * @return 校验结果，true表示校验通过（未过期），false表示校验失败（已过期）
     */
    CuaCommonResultDTO validateSignUrl(@Valid PayBizSignUrlValidateReqDTO request);
}
