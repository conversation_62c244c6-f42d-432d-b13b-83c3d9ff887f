package com.wosai.upay.job.refactor.Integration.service;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.dto.AccountChangeValidationResult;
import com.wosai.upay.job.dto.BankAccountSimpleInfoBO;
import com.wosai.upay.job.refactor.dao.AcquirerAccountChangeRuleDAO;
import com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRuleDO;
import com.wosai.upay.job.service.AcquirerAccountChangeValidationService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 收单机构测试
 *
 * <AUTHOR>
 */
@Slf4j
public class AcquirerAccountChangeTest extends BaseTest {

    @Resource
    private AcquirerAccountChangeValidationService acquirerAccountChangeValidationService;

    @Resource
    private AcquirerAccountChangeRuleDAO accountChangeRuleDAO;

    @Test
    public void testGetSharedAbilityAcquirer() {
        String merchantSn = "**************";
        BankAccountSimpleInfoBO newBankAccountSimpleInfoBO = new BankAccountSimpleInfoBO();
        newBankAccountSimpleInfoBO.setType(1);
        newBankAccountSimpleInfoBO.setHolder("马晶");
        newBankAccountSimpleInfoBO.setHolderCertificateNumber("44028120051009851X");
        newBankAccountSimpleInfoBO.setCardNumber("6210987954000116652");
        AccountChangeValidationResult accountChangeValidationResult =
                acquirerAccountChangeValidationService.validateAccountChangeWithCurrentAccount(merchantSn, AcquirerTypeEnum.LKL_V3.getValue(), newBankAccountSimpleInfoBO);
        assertThat(accountChangeValidationResult).isNotNull();

    }

    @Test
    public void testGetRule() {
        Optional<AcquirerAccountChangeRuleDO> lklRule = accountChangeRuleDAO.getByAcquirer(AcquirerTypeEnum.LKL_V3.getValue());
        assertThat(lklRule).isNotEmpty();
        Optional<AcquirerAccountChangeRuleDO> lklRule1 = accountChangeRuleDAO.getByAcquirer(AcquirerTypeEnum.LKL_V3.getValue());
        assertThat(lklRule1).isNotEmpty();
        Optional<AcquirerAccountChangeRuleDO> haikeRule = accountChangeRuleDAO.getByAcquirer(AcquirerTypeEnum.HAI_KE.getValue());
        assertThat(haikeRule).isEmpty();
    }

}
