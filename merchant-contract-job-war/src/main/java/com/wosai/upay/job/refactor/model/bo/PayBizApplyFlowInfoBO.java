package com.wosai.upay.job.refactor.model.bo;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.refactor.model.enums.PayBizApplyFlowNodeEnum;
import com.wosai.upay.job.refactor.model.enums.PayBizApplyFlowNodeStatusEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 支付业务申请流程信息BO
 *
 * <AUTHOR>
@Data
@NoArgsConstructor
public class PayBizApplyFlowInfoBO {

    /**
     * 流程节点列表
     */
    private List<PayBizApplyFlowNodeBO> nodes;

    public PayBizApplyFlowInfoBO(List<PayBizApplyFlowNodeBO> nodes) {
        this.nodes = nodes != null ? nodes : new ArrayList<>();
    }

    /**
     * 创建默认的流程信息（不包含待结算人签约）
     * 包含：进件审核 → 法人签约 → 合规补录审核 → 待生效 → 开通成功
     *
     * @return 默认流程信息
     */
    public static PayBizApplyFlowInfoBO createDefaultFlow() {
        return PayBizApplyFlowNodeEnum.createFlowInfoWithoutSettlementSign();
    }

    /**
     * 创建不包含待结算人签约的流程信息
     * 包含：进件审核 → 法人签约 → 合规补录审核 → 待生效 → 开通成功
     *
     * @return 不包含待结算人签约的流程信息
     */
    public static PayBizApplyFlowInfoBO createFlowWithoutSettlementSign() {
        return PayBizApplyFlowNodeEnum.createFlowInfoWithoutSettlementSign();
    }

    /**
     * 创建包含待结算人签约的流程信息
     * 包含：进件审核 → 法人签约 → 待结算人签约 → 合规补录审核 → 待生效 → 开通成功
     *
     * @return 包含待结算人签约的流程信息
     */
    public static PayBizApplyFlowInfoBO createFlowWithSettlementSign() {
        return PayBizApplyFlowNodeEnum.createFlowInfoWithSettlementSign();
    }

    /**
     * 创建完整的流程信息（包含所有可能的节点）
     * 包含：进件审核 → 法人签约 → 待结算人签约 → 合规补录审核 → 待生效 → 开通成功
     *
     * @return 完整的流程信息
     */
    public static PayBizApplyFlowInfoBO createCompleteFlow() {
        return PayBizApplyFlowNodeEnum.createStandardFlowInfo();
    }

    /**
     * 根据详细状态码查找节点
     *
     * @param detailStatus 详细状态码
     * @return 对应的节点，如果不存在则返回空
     */
    public Optional<PayBizApplyFlowNodeBO> findNodeByDetailStatus(Integer detailStatus) {
        if (nodes == null || detailStatus == null) {
            return Optional.empty();
        }
        return nodes.stream()
                .filter(node -> detailStatus.equals(node.getDetailStatus()))
                .findFirst();
    }

    /**
     * 标记指定状态的节点为成功
     *
     * @param detailStatus 详细状态码
     * @param finishTime   完成时间
     * @return true-标记成功，false-节点不存在
     */
    public boolean markNodeAsSuccess(Integer detailStatus, LocalDateTime finishTime) {
        Optional<PayBizApplyFlowNodeBO> nodeOpt = findNodeByDetailStatus(detailStatus);
        if (nodeOpt.isPresent()) {
            nodeOpt.get().markAsSuccess(finishTime);
            return true;
        }
        return false;
    }

    /**
     * 标记指定状态的节点为成功（使用当前时间）
     *
     * @param detailStatus 详细状态码
     * @return true-标记成功，false-节点不存在
     */
    public boolean markNodeAsSuccess(Integer detailStatus) {
        return markNodeAsSuccess(detailStatus, LocalDateTime.now());
    }

    /**
     * 标记指定状态的节点为进行中
     *
     * @param detailStatus 详细状态码
     * @return true-标记成功，false-节点不存在
     */
    public boolean markNodeAsInProgress(Integer detailStatus) {
        Optional<PayBizApplyFlowNodeBO> nodeOpt = findNodeByDetailStatus(detailStatus);
        if (nodeOpt.isPresent()) {
            nodeOpt.get().markAsInProgress();
            return true;
        }
        return false;
    }

    /**
     * 获取当前进行中的节点
     *
     * @return 当前进行中的节点，如果没有则返回空
     */
    public Optional<PayBizApplyFlowNodeBO> getCurrentNode() {
        if (nodes == null) {
            return Optional.empty();
        }
        return nodes.stream()
                .filter(PayBizApplyFlowNodeBO::isInProgress)
                .findFirst();
    }

    /**
     * 获取下一个未处理的节点
     *
     * @return 下一个未处理的节点，如果没有则返回空
     */
    public Optional<PayBizApplyFlowNodeBO> getNextUnprocessedNode() {
        if (nodes == null) {
            return Optional.empty();
        }
        return nodes.stream()
                .filter(PayBizApplyFlowNodeBO::isUnprocessed)
                .findFirst();
    }

    /**
     * 获取已完成的节点列表
     *
     * @return 已完成的节点列表
     */
    public List<PayBizApplyFlowNodeBO> getFinishedNodes() {
        if (nodes == null) {
            return new ArrayList<>();
        }
        return nodes.stream()
                .filter(PayBizApplyFlowNodeBO::isFinished)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 获取未完成的节点列表（包括未处理和进行中）
     *
     * @return 未完成的节点列表
     */
    public List<PayBizApplyFlowNodeBO> getUnfinishedNodes() {
        if (nodes == null) {
            return new ArrayList<>();
        }
        return nodes.stream()
                .filter(PayBizApplyFlowNodeBO::isUnfinished)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 获取未处理的节点列表
     *
     * @return 未处理的节点列表
     */
    public List<PayBizApplyFlowNodeBO> getUnprocessedNodes() {
        if (nodes == null) {
            return new ArrayList<>();
        }
        return nodes.stream()
                .filter(PayBizApplyFlowNodeBO::isUnprocessed)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 获取进行中的节点列表
     *
     * @return 进行中的节点列表
     */
    public List<PayBizApplyFlowNodeBO> getInProgressNodes() {
        if (nodes == null) {
            return new ArrayList<>();
        }
        return nodes.stream()
                .filter(PayBizApplyFlowNodeBO::isInProgress)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 判断是否所有节点都已完成
     *
     * @return true-所有节点都已完成，false-还有未完成的节点
     */
    public boolean isAllFinished() {
        if (nodes == null || nodes.isEmpty()) {
            return false;
        }
        return nodes.stream().allMatch(PayBizApplyFlowNodeBO::isFinished);
    }

    /**
     * 将当前进行中的节点置为成功，并将下一个未处理的节点置为进行中
     *
     * @return true-操作成功，false-没有当前节点或下一个节点
     */
    public boolean completeCurrentAndStartNext() {
        return completeCurrentAndStartNext(LocalDateTime.now());
    }

    /**
     * 将当前进行中的节点置为成功，并将下一个未处理的节点置为进行中
     *
     * @param finishTime 完成时间
     * @return true-操作成功，false-没有当前节点或下一个节点
     */
    public boolean completeCurrentAndStartNext(LocalDateTime finishTime) {
        Optional<PayBizApplyFlowNodeBO> currentNodeOpt = getCurrentNode();
        if (!currentNodeOpt.isPresent()) {
            return false; // 没有当前进行中的节点
        }

        // 标记当前节点为成功
        PayBizApplyFlowNodeBO currentNode = currentNodeOpt.get();
        currentNode.markAsSuccess(finishTime);

        // 查找下一个未处理的节点并标记为进行中
        Optional<PayBizApplyFlowNodeBO> nextNodeOpt = getNextUnprocessedNode();
        nextNodeOpt.ifPresent(PayBizApplyFlowNodeBO::markAsInProgress);

        return true;
    }

    /**
     * 将指定节点置为成功，并将下一个未处理的节点置为进行中
     *
     * @param detailStatus 要完成的节点状态码
     * @return true-操作成功，false-节点不存在或操作失败
     */
    public boolean completeNodeAndStartNext(Integer detailStatus) {
        return completeNodeAndStartNext(detailStatus, LocalDateTime.now());
    }

    /**
     * 将指定节点置为成功，并将下一个未处理的节点置为进行中
     *
     * @param detailStatus 要完成的节点状态码
     * @param finishTime 完成时间
     * @return true-操作成功，false-节点不存在或操作失败
     */
    public boolean completeNodeAndStartNext(Integer detailStatus, LocalDateTime finishTime) {
        Optional<PayBizApplyFlowNodeBO> nodeOpt = findNodeByDetailStatus(detailStatus);
        if (!nodeOpt.isPresent()) {
            return false; // 节点不存在
        }

        // 标记指定节点为成功
        PayBizApplyFlowNodeBO node = nodeOpt.get();
        node.markAsSuccess(finishTime);

        // 查找下一个未处理的节点并标记为进行中
        Optional<PayBizApplyFlowNodeBO> nextNodeOpt = getNextUnprocessedNode();
        if (nextNodeOpt.isPresent()) {
            nextNodeOpt.get().markAsInProgress();
        }

        return true;
    }

    /**
     * 获取流程完成进度（百分比）
     *
     * @return 完成进度，0-100
     */
    public int getProgress() {
        if (nodes == null || nodes.isEmpty()) {
            return 0;
        }
        long finishedCount = nodes.stream().mapToLong(node -> node.isFinished() ? 1 : 0).sum();
        return (int) (finishedCount * 100 / nodes.size());
    }

    /**
     * 转换为JSON字符串
     *
     * @return JSON字符串
     */
    public String toJsonString() {
        return JSON.toJSONString(this.nodes);
    }

    /**
     * 从JSON字符串解析
     *
     * @param jsonString JSON字符串
     * @return 流程信息BO
     */
    public static PayBizApplyFlowInfoBO fromJsonString(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return new PayBizApplyFlowInfoBO();
        }
        try {
            List<PayBizApplyFlowNodeBO> nodes = JSON.parseArray(jsonString, PayBizApplyFlowNodeBO.class);
            return new PayBizApplyFlowInfoBO(nodes);
        } catch (Exception e) {
            // 解析失败时返回空的流程信息
            return new PayBizApplyFlowInfoBO();
        }
    }

    /**
     * 添加节点
     *
     * @param node 要添加的节点
     */
    public void addNode(PayBizApplyFlowNodeBO node) {
        if (this.nodes == null) {
            this.nodes = new ArrayList<>();
        }
        this.nodes.add(node);
    }

    /**
     * 获取节点数量
     *
     * @return 节点数量
     */
    public int getNodeCount() {
        return nodes != null ? nodes.size() : 0;
    }

    /**
     * 判断流程是否包含待结算人签约节点
     *
     * @return true-包含待结算人签约节点，false-不包含
     */
    public boolean hasSettlementSignNode() {
        return findNodeByDetailStatus(PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode()).isPresent();
    }

    /**
     * 动态添加待结算人签约节点（在法人签约之后）
     * 如果已存在则不重复添加
     *
     * @return true-添加成功，false-已存在或添加失败
     */
    public boolean addSettlementSignNode() {
        if (hasSettlementSignNode()) {
            return false; // 已存在，不重复添加
        }

        if (nodes == null) {
            nodes = new ArrayList<>();
        }

        // 查找法人签约节点的位置
        int legalSignIndex = -1;
        for (int i = 0; i < nodes.size(); i++) {
            if (PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN.getCode().equals(nodes.get(i).getDetailStatus())) {
                legalSignIndex = i;
                break;
            }
        }

        if (legalSignIndex == -1) {
            // 如果没有找到法人签约节点，添加到末尾
            addNode(PayBizApplyFlowNodeEnum.SETTLEMENT_SIGN.createFlowNodeBO());
        } else {
            // 在法人签约节点之后插入（未处理状态）
            nodes.add(legalSignIndex + 1, PayBizApplyFlowNodeEnum.SETTLEMENT_SIGN.createFlowNodeBO());
        }

        return true;
    }

    /**
     * 移除待结算人签约节点
     *
     * @return true-移除成功，false-节点不存在
     */
    public boolean removeSettlementSignNode() {
        if (nodes == null) {
            return false;
        }

        return nodes.removeIf(node ->
            PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode().equals(node.getDetailStatus()));
    }

    /**
     * 将所有未完成的节点（待处理/进行中）置为成功
     *
     * @param successTime 成功时间
     */
    public void markAllUnfinishedNodesAsSuccess(LocalDateTime successTime) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        for (PayBizApplyFlowNodeBO node : nodes) {
            // 如果节点是待处理或进行中状态，将其置为成功
            if (PayBizApplyFlowNodeStatusEnum.UNPROCESSED.getCode().equals(node.getStatus()) ||
                PayBizApplyFlowNodeStatusEnum.IN_PROGRESS.getCode().equals(node.getStatus())) {

                node.setStatus(PayBizApplyFlowNodeStatusEnum.SUCCESS.getCode());
                node.setTime(successTime);
            }
        }
    }

    /**
     * 将所有未完成的节点（待处理/进行中）置为成功（使用当前时间）
     */
    public void markAllUnfinishedNodesAsSuccess() {
        markAllUnfinishedNodesAsSuccess(LocalDateTime.now());
    }

    /**
     * 根据条件创建流程信息
     *
     * @param needSettlementSign 是否需要待结算人签约节点
     * @return 对应的流程信息
     */
    public static PayBizApplyFlowInfoBO createFlowByCondition(boolean needSettlementSign) {
        return needSettlementSign ? createFlowWithSettlementSign() : createFlowWithoutSettlementSign();
    }
}
