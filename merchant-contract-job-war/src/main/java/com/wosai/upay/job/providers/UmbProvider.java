package com.wosai.upay.job.providers;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.bankDirect.UmbDirectBiz;
import com.wosai.upay.job.enume.BankDirectApplyRefEnum;
import com.wosai.upay.job.enume.BankDirectApplyViewStatusEnum;
import com.wosai.upay.job.enume.ProviderTerminalBindLevel;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.helper.DateHelper;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.model.providerterminal.ProviderTerminalAcquirerMerchantInfo;
import com.wosai.upay.job.model.providerterminal.ProviderTerminalAddRequest;
import com.wosai.upay.job.model.umb.UmbMerchantAcquirerInfo;
import com.wosai.upay.job.refactor.dao.MerchantAcquirerInfoDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantAcquirerInfoDO;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.service.ProviderTerminalSerivce;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.constant.CommonConstant;
import com.wosai.upay.merchant.contract.constant.Constant;
import com.wosai.upay.merchant.contract.enume.umb.UMBContractStatus;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.provider.UMBParam;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.service.UmbService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.BiConsumer;

import static com.wosai.upay.job.constant.CallBackConstants.CONTRACT_CALLBACK_MSG;
import static com.wosai.upay.job.model.CommonModel.PROVIDER_MCH_ID;
import static com.wosai.upay.merchant.contract.model.MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL;

@Slf4j
@Component(ProviderUtil.UMB_CHANNEL)
public class UmbProvider extends AbstractProvider {

    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Lazy
    @Autowired
    UmbDirectBiz umbDirectBiz;

    @Autowired
    private UmbService umbService;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private MerchantAcquirerInfoDAO merchantAcquirerInfoDAO;

    @Autowired
    private ProviderTerminalSerivce providerTerminalSerivce;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private AcquirerService acquirerService;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        int influPtask = AcquirerTypeEnum.UMB.getValue().equals(acquirer) || isSubBiz(merchantSn, AcquirerTypeEnum.UMB.getValue()) ? contractRule.getUpdateInfluPtask() : 0;

        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(influPtask)
                .setChannel(ProviderUtil.UMB_CHANNEL)
                .setChange_config(0)
                .setDefault_channel(contractRule.getDefault())
                .setPayway(contractRule.getPayway())
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setRetry(0);
        Integer taskType = null;
        if (PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
            //银行账户变更
            if (ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == event.getEvent_type()) {
                Map requestParam = (Map) paramContext.get("cardRequestParam");
                if (!CollectionUtils.isEmpty(requestParam)) {
                    //银行卡管理服务发起的变更(merchant_bank_account_pre)
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE;
                } else {
                    //dts订阅直接变更(merchant_bank_account)
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                }
            } else if (ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION == event.getEvent_type()) {
                //更新基本信息
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
            } else if (ContractEvent.OPT_TYPE_NET_CRM_UPDATE == event.getEvent_type()) {
                String crmUpdate = BeanUtil.getPropString(paramContext, "crmUpdate");
                if (!org.springframework.util.StringUtils.isEmpty(crmUpdate)) {
                    switch (crmUpdate) {
                        case "0":
                            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
                            break;
                        case "1":
                            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                            break;
                        case "2":
                            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH;
                            break;
                        default:
                            break;
                    }
                }
            } else if (ContractEvent.OPT_TYPE_UPDATE_BUSINESS_LICENSE == event.getEvent_type()) {
                //更新营业执照
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE;
            } else if (ContractEvent.OPT_TYPE_MERCHANT_FEERATE == event.getEvent_type()) {
                ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(event.getMerchant_sn());
                if (!AcquirerTypeEnum.UMB.getValue().equals(contractStatus.getAcquirer()) || event.getEvent_msg().contains("app_id")) {
                    return null;
                }
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE;
            }
            if (taskType == null) {
                return null;
            }
        }
        return subTask.setTask_type(taskType);
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask contractSubTask) {
        UMBParam umbParam = contractParamsBiz.buildParam(contractChannel, contractSubTask, UMBParam.class);
        ContractResponse contractResponse = new ContractResponse();
        Integer payWay = contractSubTask.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        ContractRule contractRule = ruleContext.getContractRule(contractSubTask.getContract_rule());
        if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(contractTask.getType())) {
            String contractId = merchantAcquirerInfoDAO.getByMerchantSnAndAcquirer(contractTask.getMerchant_sn(), AcquirerTypeEnum.UMB.getValue())
                    .map(MerchantAcquirerInfoDO::getAcquirerMerchantId).orElse(null);
            String merchantInfo = merchantAcquirerInfoDAO.getByMerchantSnAndAcquirer(contractTask.getMerchant_sn(), AcquirerTypeEnum.UMB.getValue())
                    .map(MerchantAcquirerInfoDO::getMerchantInfo).orElse(null);
            Optional<UMBContractStatus> umbContractStatusOptional = Optional.empty();
            if (StrUtil.isNotEmpty(merchantInfo)) {
                UmbMerchantAcquirerInfo umbMerchantAcquirerInfo = JSON.parseObject(merchantInfo, UmbMerchantAcquirerInfo.class);
                String detailedStatus = umbMerchantAcquirerInfo.getDetailed_status();
                umbContractStatusOptional = Optional.of(UMBContractStatus.getByCode(detailedStatus));
            }
            if (Objects.equals(payWay, PaywayEnum.ACQUIRER.getValue())) {
                //如果是从上一次进件的位置继续进件,那么这个要考虑当前已经的进件状态.设置某些任务,直接返回200.
                switch (contractRule.getType()) {
                    case 1:
                        if (umbContractStatusOptional.isPresent() && umbContractStatusOptional.get().getCodeAsInt() > 1) {
                            return ContractResponse.defaultSuccessContractResponse;
                        }
                        contractResponse = submitInformation(contractTask, contextParam, umbParam);
                        break;
                    case 3:
                        if (umbContractStatusOptional.isPresent() && umbContractStatusOptional.get().getCodeAsInt() > 2) {
                            return ContractResponse.defaultSuccessContractResponse;
                        }
                        contractResponse = AddBankCard(contractTask.getMerchant_sn(), contextParam, umbParam, contractId);
                        break;
                    case 4:
                        if (umbContractStatusOptional.isPresent() && umbContractStatusOptional.get().getCodeAsInt() > 3) {
                            return ContractResponse.defaultSuccessContractResponse;
                        }
                        contractResponse = assign(contractTask.getMerchant_sn(), contextParam, umbParam, contractId);
                        break;
                    case 5:
                        if (umbContractStatusOptional.isPresent() && umbContractStatusOptional.get().getCodeAsInt() > 4) {
                            return ContractResponse.defaultSuccessContractResponse;
                        }
                        contractResponse = profitSharing(contractSubTask, contextParam, contractId);
                }
                String merchantId = BeanUtil.getPropString(contextParam, "merchant.id");
                updateClearProvider(merchantId, contractTask.getMerchant_sn());
                return contractResponse;
            } else {
                // 其他支付类型：微信，支付宝、云闪付
                String mchid = (String) contractChannel.getAcquirer_metadata().get("mchid");
                Optional<MerchantAcquirerInfoDO> merchantAcquirerInfoOpt = merchantAcquirerInfoDAO.getByMerchantSnAndAcquirer(contractTask.getMerchant_sn(), AcquirerTypeEnum.UMB.getValue());
                if (merchantAcquirerInfoOpt.isPresent()) {
                    contractId = merchantAcquirerInfoOpt.get().getAcquirerMerchantId();
                }
                contractResponse = umbService.contractMerchantOtherPayWay(umbParam, payWay, mchid, contractChannel.getChannel_no(), contractId, contractSubTask);
            }
            return contractResponse;
        }
        return null;
    }

    @NotNull
    private ContractResponse submitInformation(ContractTask contractTask, Map<String, Object> contextParam, UMBParam umbParam) {
        ContractResponse contractResponse;
        contractResponse = umbService.contractMerchant(umbParam, contextParam);
        if (contractResponse.isSuccess()) {
            updateStatusToContractTask(contractTask.getMerchant_sn(), UMBContractStatus.CONTRACT, (String) contractResponse.getResponseParam().get("merid"));
            final BankDirectApply apply = bankDirectApplyMapper.getApplyByTaskId(contractTask.getId());
            // 已提交待银行审核
            umbDirectBiz.recordViewProcess(apply, BankDirectApplyViewStatusEnum.DISTRIBUTING.getValue(), new Date());
        }
        return contractResponse;
    }

    @NotNull
    private ContractResponse AddBankCard(String merchantSn, Map<String, Object> contextParam, UMBParam umbParam, String contractId) {
        ContractResponse contractResponse;
        contractResponse = umbService.addBankCard(umbParam, contextParam, contractId);
        saveCurrentContractStatusIfSuccess(contractResponse, merchantSn, UMBContractStatus.BIND_CARD);
        return contractResponse;
    }

    @NotNull
    private ContractResponse assign(String merchantSn, Map<String, Object> contextParam, UMBParam umbParam, String contractId) {
        ContractResponse contractResponse;
        contractResponse = umbService.assign(umbParam, contractId, contextParam);
        saveCurrentContractStatusIfSuccess(contractResponse, merchantSn, UMBContractStatus.SIGN);
        return contractResponse;
    }

    private void saveCurrentContractStatusIfSuccess(ContractResponse contractResponse, String merchantSn, UMBContractStatus umbContractStatus) {
        if (contractResponse.isSuccess()) {
            updateStatusToContractTask(merchantSn, umbContractStatus, null);
        }
    }

    @NotNull
    private ContractResponse profitSharing(ContractSubTask contractSubTask, Map<String, Object> contextParam, String contractId) {
        ContractResponse contractResponse;
        contractResponse = umbService.shareProfit(contractSubTask, contextParam, contractId);
        saveCurrentContractStatusIfSuccess(contractResponse, contractSubTask.getMerchant_sn(), UMBContractStatus.PROFITSHARING);
        return contractResponse;
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        UMBParam umbParam = super.buildParam(contractChannel, sub, UMBParam.class);
        Map<String, Object> contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        Integer taskType = sub.getTask_type();
        if (ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(taskType)
                || ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE.equals(taskType) ||
                ContractSubTaskTypeEnum.STORE_PHOTO_UPDATE.getValue().equals(taskType)) {
            return umbService.updateMerchantMainInfo(contextParam, umbParam);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION.equals(taskType)) {
            return umbService.updateMerchantBasicInfo(contextParam, umbParam);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(taskType)
                || ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(taskType)) {
            //当前是中投商户,且卡为民生银行卡才执行换卡逻辑,否则直接返回失败.
            Map<String, Object> bankAccount = (Map) contextParam.get(CommonConstant.KEY_BANK_ACCOUNT);

            // 检查是否为中投商户
            boolean isUmbMerchant = (null != merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(sub.getMerchant_sn(), ProviderEnum.PROVIDER_UMB.getValue()));

            // 检查是否为民生银行卡
            boolean isCmbcBank = (bankAccount != null && BankDirectApplyRefEnum.CMBC.getText().equals(bankAccount.get(MerchantBankAccount.BANK_NAME)));

            if (isUmbMerchant && isCmbcBank) {
                log.info("[UmbProvider.processUpdateTaskByRule] merchant is umb merchant and bank card is cmbc, update bank card for merchantSn:{}", sub.getMerchant_sn());
                return umbService.updateMerchantBankAccount(contextParam, umbParam);
            } else {
                log.info("[UmbProvider.processUpdateTaskByRule] merchant is not umb merchant or bank card is not cmbc, skip update bank card for merchantSn:{}", sub.getMerchant_sn());
                return ContractResponse.builder().code(460).message("商户在中投通道侧,只支持绑定、修改民生银行结算卡").build();
            }
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE.equals(taskType)) {
            return umbService.updateMerchantFeeRate(contextParam, umbParam);
        }
        return null;
    }

    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        ContractChannel contractChannel = ruleContext.getContractRule(merchantProviderParams.getContract_rule()).getContractChannel();
        String mchid = (String) contractChannel.getAcquirer_metadata().get("mchid");
        UMBParam umbParam = contractParamsBiz.buildParam(contractChannel, null, UMBParam.class);
        return umbService.wechatSubDevConfig(weixinConfig, mchid, umbParam, merchantProviderParams.getProvider_merchant_id());
    }

    public HandleQueryStatusResp doHandleContractStatus(ContractSubTask contractSubTask, ContractResponse response, UMBContractStatus status) {
        //失败 修改状态
        if (response.isBusinessFail()) {
            return new HandleQueryStatusResp()
                    .setFail(true)
                    .setMessage(response.getMessage());
        } else if (response.isSystemFail()) {
            //可重试异常 不做任何处理
            return getSystemFailResp(response);
        } else {
            final ContractTask task = contractTaskMapper.selectByPrimaryKey(contractSubTask.getP_task_id());

            updateStatusToContractTask(task.getMerchant_sn(), status, contractSubTask.getContract_id());

            Map<String, Object> callbackMsg = response.getResponseParam();
            String originResult = contractSubTask.getResult();
            this.updateSubAndParentTaskResult(contractSubTask.getId(), originResult, "处理成功");

            Map<String, Object> resp = JSON.parseObject(contractSubTask.getResponse_body(), Map.class);
            ArrayList<Map<String, Object>> callBack = Lists.newArrayList(callbackMsg);
            if (resp == null) {
                resp = new HashMap<>();
            }
            resp.put(CONTRACT_CALLBACK_MSG, callBack);
            contractSubTask.setResponse_body(JSONObject.toJSONString(resp));

            return new HandleQueryStatusResp()
                    .setSuccess(true)
                    .setRetry(true)
                    .setMessage(status.getSuccessDesc());
        }
    }

    public void handleCallBackSuccess(ContractSubTask contractSubTask, String providerMerchantId, boolean shareProfit) {
        UMBParam umbParam = contractParamsBiz.buildContractParams(contractSubTask.getRule_group_id(), UMBParam.class);
        UmbProvider o = (UmbProvider) AopContext.currentProxy();
        MerchantProviderParams acquirerParams = o.saveAcquirerProviderParams(contractSubTask, umbParam, providerMerchantId);
        String merchantSn = acquirerParams.getMerchant_sn();
        this.setShareProfitStatus(merchantSn, shareProfit);
        providerTerminalSerivce.addProviderTerminal(new ProviderTerminalAddRequest()
                .setMerchantSn(contractSubTask.getMerchant_sn())
                .setBindLevel(ProviderTerminalBindLevel.MERCHANT)
                .setAcquirerMerchantInfo(new ProviderTerminalAcquirerMerchantInfo()
                        .setMerchantSn(merchantSn)
                        .setAcquirerMerchantId(acquirerParams.getPay_merchant_id())
                        .setProvider(ProviderEnum.PROVIDER_UMB)));
    }

    /*
    将商户分账情况更新到merchant_acquirer_info.
     */
    public void updateStatusToContractTask(String merchantSn, UMBContractStatus status, String acquirerMerchantId) {
        updateUmbMerchantAcquirerInfo(merchantSn, status, (umbInfo, infoDO) -> {
            if (UMBContractStatus.CONTRACT.equals(status) && StrUtil.isNotEmpty(acquirerMerchantId)) {
                infoDO.setAcquirerMerchantId(acquirerMerchantId);
            }
        });
    }

    /**
     * 更新进件的详细状态到merchant_acquirer_info中.
     *
     * @param merchantSn 进件商户sn
     * @param status     当前进件详细状态
     */
    private void updateUmbMerchantAcquirerInfo(String merchantSn, UMBContractStatus status, BiConsumer<UmbMerchantAcquirerInfo, MerchantAcquirerInfoDO> updateAction) {
        Optional<MerchantAcquirerInfoDO> merchantAcquirerInfoOpt = merchantAcquirerInfoDAO.getByMerchantSnAndAcquirer(merchantSn, AcquirerTypeEnum.UMB.getValue());
        UmbMerchantAcquirerInfo umbMerchantAcquirerInfo;
        MerchantAcquirerInfoDO acquirerInfoDO = new MerchantAcquirerInfoDO();
        if (merchantAcquirerInfoOpt.isPresent()) {
            acquirerInfoDO = merchantAcquirerInfoOpt.get();
            umbMerchantAcquirerInfo = JSON.parseObject(acquirerInfoDO.getMerchantInfo(), UmbMerchantAcquirerInfo.class);
        } else {
            acquirerInfoDO.setMerchantSn(merchantSn);
            acquirerInfoDO.setAcquirer(AcquirerTypeEnum.UMB.getValue());
            umbMerchantAcquirerInfo = new UmbMerchantAcquirerInfo();
        }

        umbMerchantAcquirerInfo.setDetailed_status(status.getCode());
        if (updateAction != null) {
            updateAction.accept(umbMerchantAcquirerInfo, acquirerInfoDO);
        }

        acquirerInfoDO.setMerchantInfo(umbMerchantAcquirerInfo.toJsonStr());
        merchantAcquirerInfoDAO.insertOrUpdateOne(acquirerInfoDO);
    }

    private void setShareProfitStatus(String merchantSn, boolean shareProfit) {
        updateUmbMerchantAcquirerInfo(merchantSn, UMBContractStatus.PROFITSHARING, (umbInfo, infoDO) -> umbInfo.setShare_profit(shareProfit));
    }

    private static HandleQueryStatusResp getSystemFailResp(ContractResponse response) {
        return new HandleQueryStatusResp()
                .setRetry(true)
                .setMessage(response.getMessage());
    }

    private void updateSubAndParentTaskResult(Long id, String originResult, String targetResult) {
        if (Objects.equals(originResult, targetResult)) {
            return;
        }
        ContractSubTask subTask = new ContractSubTask();
        subTask.setId(id);
        subTask.setResult(targetResult);
        contractSubTaskMapper.updateByPrimaryKey(subTask);
        //为了保持contractTask同步;
        final ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(id);
        final Long pTaskId = contractSubTask.getP_task_id();
        final ContractTask task = contractTaskMapper.selectByPrimaryKey(pTaskId);
        if (Objects.equals(task.getResult(), targetResult)) {
            return;
        }
        //只有影响主任务的时候才会去更新主任务的中的result
        if (Objects.equals(contractSubTask.getStatus_influ_p_task(), 1)) {
            final ContractTask contractTask = new ContractTask();
            contractTask.setId(pTaskId);
            contractTask.setResult(targetResult);
            contractTask.setPriority(task.getPriority());
            contractTaskMapper.updateByPrimaryKey(contractTask);
        }
    }

    @Override
    public ContractResponse boundTerminal(AddTermInfoDTO addTermInfoDTO, int payWay, String terminalSn) {
        ContractResponse response = new ContractResponse();
        String merchantSn = addTermInfoDTO.getMerchantSn();
        String providerMerNo = addTermInfoDTO.getProviderMerNo();
        MerchantProviderParams providerParams = merchantProviderParamsMapper.getParamsByProviderMerchantIdAndProviderAndPayWay(providerMerNo,
                String.valueOf(ProviderEnum.PROVIDER_UMB.getValue()),
                String.valueOf(payWay));
        if (providerParams == null) {
            response.setMessage("未查询到商户的交易参数,无法绑定终端");
            response.setCode(Constant.RESULT_CODE_BIZ_EXCEPTION);
            return response;
        }
        UMBParam umbParam = contractParamsBiz.buildContractParamsByParams(providerParams, UMBParam.class);
        return this.addTerminalToUmb(merchantSn, providerMerNo, String.valueOf(payWay), addTermInfoDTO.getDeviceId(), umbParam);
    }

    private ContractResponse addTerminalToUmb(String merchantSn, String providerMerNo, String payWay, String terminalSn, UMBParam umbParam) {
        Optional<MerchantAcquirerInfoDO> byMerchantSnAndAcquirer = merchantAcquirerInfoDAO.getByMerchantSnAndAcquirer(merchantSn, AcquirerTypeEnum.UMB.getValue());
        if (!byMerchantSnAndAcquirer.isPresent()) {
            log.error("中投科信商户信息不存在,商户号:{}", merchantSn);
        }
        return umbService.bindTerminal(merchantSn, providerMerNo, payWay, terminalSn, umbParam);
    }

    @Override
    protected ContractResponse queryContractStatus(ContractSubTask contractSubTask) {
        final ContractChannel contractChannel = ruleContext.getContractRule(contractSubTask.getContract_rule()).getContractChannel();
        UMBParam umbParam = contractParamsBiz.buildParam(contractChannel, contractSubTask, UMBParam.class);
        ContractResponse response = umbService.queryMerchantStatus(contractSubTask.getMerchant_sn(), contractSubTask.getContract_id(), umbParam);
        if (response.isSystemFail()) {
            ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(contractSubTask.getP_task_id());
            if (DateHelper.isOverOneMonth(contractTask.getCreate_at())) {
                response.setCode(460).setMessage("任务等待时长超过一个月，中止任务");
            }
        }
        return response;
    }

    @Override
    protected HandleQueryStatusResp handleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        return this.doHandleContractStatus(contractSubTask, response, UMBContractStatus.PROFITSHARING);
    }

    /**
     * 收钱吧门店绑定
     *
     * @param storeSn    门店号
     * @param merchantSn 商户号
     * @param provider   收单机构provider
     */
    @Override
    public void handleSqbStoreTerminal(String storeSn, String merchantSn, Integer provider) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_UMB.getValue());
        providerTerminalSerivce.addProviderTerminal(new ProviderTerminalAddRequest()
                .setMerchantSn(merchantSn)
                .setStoreSn(storeSn)
                .setBindLevel(ProviderTerminalBindLevel.STORE)
                .setAcquirerMerchantInfo(new ProviderTerminalAcquirerMerchantInfo()
                        .setMerchantSn(acquirerParams.getMerchant_sn())
                        .setAcquirerMerchantId(acquirerParams.getPay_merchant_id())
                        .setProvider(ProviderEnum.PROVIDER_UMB)));
    }

    @Override
    public void handleSqbTerminalBind(MerchantInfo merchant, Map terminal, Integer provider) {
        final String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        StoreInfo store = storeService.getStoreById(storeId, null);
        final String storeSn = store.getSn();
        final String merchantSn = merchant.getSn();
        final String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_UMB.getValue());
        providerTerminalSerivce.addProviderTerminal(new ProviderTerminalAddRequest()
                .setMerchantSn(merchantSn)
                .setStoreSn(storeSn)
                .setTerminalSn(terminalSn)
                .setBindLevel(ProviderTerminalBindLevel.TERMINAL)
                .setAcquirerMerchantInfo(new ProviderTerminalAcquirerMerchantInfo()
                        .setMerchantSn(acquirerParams.getMerchant_sn())
                        .setAcquirerMerchantId(acquirerParams.getPay_merchant_id())
                        .setProvider(ProviderEnum.PROVIDER_UMB)));
    }

    @Override
    public void createProviderTerminal(String merchantSn, Integer provider) {
        doCreateProviderTerminal(merchantSn, provider);
    }


    @Transactional(rollbackFor = Exception.class)
    public MerchantProviderParams saveAcquirerProviderParams(ContractSubTask contractSubTask, UMBParam umbParam, String payWaySubMerchantNo) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getParamsByProviderMerchantIdAndProviderAndPayWay(
                payWaySubMerchantNo, umbParam.getProvider(), String.valueOf(PaywayEnum.ACQUIRER));
        if (Objects.nonNull(acquirerParams)) {
            return acquirerParams;
        }
        MerchantProviderParams params = this.buildMerchantProviderParams(payWaySubMerchantNo, contractSubTask, umbParam);
        params.setExtra(CommonUtil.map2Bytes(CollectionUtil.hashMap(
                CommonModel.TRADE_PARAMS, CollectionUtil.hashMap(
                        PROVIDER_MCH_ID, payWaySubMerchantNo,
                        "platform_mch_id", umbParam.getChannel_no()
                ))));
        merchantProviderParamsMapper.insertSelective(params);
        return params;
    }


    private MerchantProviderParams buildMerchantProviderParams(String providerMerchantId, ContractSubTask contractSubTask, UMBParam umbParam) {
        long time = System.currentTimeMillis();
        MerchantProviderParams merchantProviderParams = new MerchantProviderParams();
        merchantProviderParams.setId(UUID.randomUUID().toString());
        merchantProviderParams.setMerchant_sn(contractSubTask.getMerchant_sn());
        merchantProviderParams.setOut_merchant_sn(providerMerchantId);
        merchantProviderParams.setChannel_no(umbParam.getChannel_no());
        merchantProviderParams.setParent_merchant_id(umbParam.getChannel_no());
        merchantProviderParams.setProvider(Integer.valueOf(umbParam.getProvider()));
        merchantProviderParams.setProvider_merchant_id(providerMerchantId);

        merchantProviderParams.setRule_group_id(contractSubTask.getRule_group_id());
        //这里不想使用umb-share-profit来作为产生payway=0时的rule(需要将rule-share-profit的isupdate 设置为true才能处理更新事件.).所以用rule_group_id作为rule.
        merchantProviderParams.setContract_rule(contractSubTask.getRule_group_id());
        merchantProviderParams.setUpdate_status(1);

        merchantProviderParams.setPayway(PaywayEnum.ACQUIRER.getValue());
        merchantProviderParams.setPay_merchant_id(providerMerchantId);
        merchantProviderParams.setParams_config_status(PARAMS_CONFIG_STATUS_NULL);
        merchantProviderParams.setCtime(time);
        merchantProviderParams.setMtime(time);
        return merchantProviderParams;
    }

}
