package com.wosai.upay.job.xxljob.direct.tonglianv2;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.PayBizApplyDAO;
import com.wosai.upay.job.refactor.dao.PayBizApplyLogDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.job.xxljob.context.TonglianV2Context;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.template.AbstractDirectJobHandler;
import com.wosai.upay.merchant.contract.model.provider.TongLianV2Param;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.Random;

/**
 * 通联收银宝V2定时任务抽象基类
 * 提取公共方法，避免代码重复
 *
 * <AUTHOR>
 * @date 2025/9/15
 */
@Slf4j
public abstract class AbstractTonglianV2JobHandler extends AbstractDirectJobHandler {

    @Autowired
    protected PayBizApplyDAO payBizApplyDAO;

    @Autowired
    protected PayBizApplyLogDAO payBizApplyLogDAO;

    @Autowired
    protected MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    protected ContractParamsBiz contractParamsBiz;

    @Autowired
    protected TongLianV2Service tongLianV2Service;

    @Autowired
    protected DirectStatusBiz directStatusBiz;

    /**
     * 基础延迟时间（分钟）
     */
    protected static final int BASE_DELAY_MINUTES_1_3_DAYS = 5;
    protected static final int BASE_DELAY_MINUTES_4_7_DAYS = 30;

    /**
     * 随机延迟范围（分钟）
     */
    protected static final int RANDOM_DELAY_RANGE = 10;

    /**
     * 随机数生成器
     */
    protected static final Random RANDOM = new Random();

    @Override
    public void execute(DirectJobParam param) {
        long startTime = System.currentTimeMillis();
        int successCount = 0;
        int failureCount = 0;

        try {
            // 查询待处理的申请记录（由子类实现具体查询逻辑）
            List<PayBizApplyDO> pendingApplies = queryPendingApplies(param);

            log.info("查询到 {} 条待处理的申请记录", pendingApplies.size());

            // 处理每条申请记录
            for (PayBizApplyDO apply : pendingApplies) {
                try {
                    TonglianV2Context context = TonglianV2Context.create(apply);
                    processApply(context);
                    successCount++;
                } catch (Exception e) {
                    failureCount++;
                    log.error("处理申请单失败，applyId: {}, merchantSn: {}",
                            apply.getId(), apply.getMerchantSn(), e);
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            log.info("{}执行完成，总记录数: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                    getJobName(), pendingApplies.size(), successCount, failureCount, duration);

        } catch (Exception e) {
            log.error("{}执行异常", getJobName(), e);
            throw e;
        }
    }

    /**
     * 前置校验并准备上下文
     */
    protected boolean validateAndPrepareContext(TonglianV2Context context) {
        if (context.isValidated()) {
            return true;
        }

        PayBizApplyDO apply = context.getApply();
        try {
            // 获取商户参数
            Optional<MerchantProviderParamsDO> acquirerParams = merchantProviderParamsDAO
                    .getMerchantProviderParamsByProviderAndPayway(apply.getMerchantSn(),
                            ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), PaywayEnum.ACQUIRER.getValue());

            if (!acquirerParams.isPresent()) {
                log.error("收单机构参数不存在，applyId: {}, merchantSn: {}", apply.getId(), apply.getMerchantSn());
                return false;
            }

            MerchantProviderParamsDO merchantProviderParam = acquirerParams.get();
            context.setMerchantProviderParam(merchantProviderParam);

            // 构建通联参数
            TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParams(
                    merchantProviderParam.getProvider() + "",
                    merchantProviderParam.getPayway(),
                    merchantProviderParam.getChannelNo(),
                    TongLianV2Param.class);
            context.setTongLianV2Param(tongLianV2Param);

            context.setValidated(true);
            return true;

        } catch (Exception e) {
            log.error("前置校验异常，applyId: {}", apply.getId(), e);
            return false;
        }
    }

    /**
     * 查询待处理的申请记录（由子类实现具体查询逻辑）
     */
    protected abstract List<PayBizApplyDO> queryPendingApplies(DirectJobParam param);

    /**
     * 处理单个申请记录（由子类实现具体处理逻辑）
     */
    protected abstract void processApply(TonglianV2Context context);

    /**
     * 获取任务名称（用于日志输出）
     */
    protected abstract String getJobName();

}
