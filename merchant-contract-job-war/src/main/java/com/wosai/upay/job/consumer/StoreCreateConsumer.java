package com.wosai.upay.job.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.store.basic.StoreBasicInsertEvent;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.LklV3ShopTermBiz;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.externalservice.brand.BrandBusinessClient;
import com.wosai.upay.job.externalservice.brand.model.BrandDetailInfoQueryResp;
import com.wosai.upay.job.externalservice.brand.model.BrandMerchantInfoQueryResp;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.AcquirerMerchantDto;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.LklV3Provider;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.JacksonHelperUtils;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.collections.MapUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 消费创建门店的消息，用于lklv3 的增网增终进件
 * <AUTHOR>
 * @Date 2021/4/26 3:37 下午
 **/
@Component
@Slf4j
@ConditionalOnProperty(name = "consumer.init", havingValue = "true")
public class StoreCreateConsumer extends AbstractDataBusConsumer {

    @Autowired
    LklV3ShopTermBiz lklV3ShopTermBiz;

    @Autowired
    StoreService storeService;
    @Autowired
    com.wosai.upay.core.service.StoreService cStoreService;
    @Autowired
    MerchantService merchantService;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    ContractTaskBiz contractTaskBiz;

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    LklV3Provider lklV3Provider;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    @Autowired
    ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    ProviderTerminalBiz providerTerminalBiz;

    @Autowired
    @Lazy
    MerchantProviderParamsService merchantProviderParamsService;

    @Autowired
    private BrandBusinessClient brandBusinessClient;

    @KafkaListener(topics = "#{'${databus.consumer.store.topic}'.split(',')}", containerFactory = "dataBusKafkaListenerContainerFactoryV2")
    @Transactional
    public void consume(ConsumerRecord<String, GenericRecord> record) {
        handle(record);
    }

    @Override
    protected void doHandleEvent(AbstractEvent event) {
        log.info("start handling storeInsert switcher:{}, event: {}", applicationApolloConfig.getLklV3ShopTermSwitch(), JacksonHelperUtils.toJsonString(event));
        if (!applicationApolloConfig.getLklV3ShopTermSwitch()) {
            return;
        }
        if (!(event instanceof StoreBasicInsertEvent)) {
            return;
        }
        StoreBasicInsertEvent insertEvent = (StoreBasicInsertEvent) event;
        StoreInfo store = storeService.getStoreBySn(insertEvent.getStoreSn(), devCode);
        if (Objects.isNull(store)) {
            return;
        }
        //不会处理lklV3
        do259StoreBind(store.getSn());
        String merchantId = store.getMerchant_id();
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, devCode);
        String merchantSn = merchant.getSn();
        List<AcquirerMerchantDto> acquirerList = merchantProviderParamsService.getAcquirerMerchantInfo(merchantId);
        //是否成功进件拉卡拉
        boolean match = acquirerList.parallelStream().anyMatch(info -> info.getAcquirer().contains(AcquirerTypeEnum.LKL_V3.getValue()));
        if (!match) {
            log.info("商户:{},没有成功进件拉卡拉", merchantId);
            return;
        }
        //只要报过了拉卡拉就要增终
//        if (weixinDirectParamsBiz.checkDirect(merchantSn, PaywayEnum.ALIPAY.getValue()) || weixinDirectParamsBiz.checkDirect(merchantSn, PaywayEnum.WEIXIN.getValue())) {
//            return;
//        }
        Map firstStore = lklV3ShopTermBiz.findFirstStore(store.getMerchant_id());
        if (store.getSn().equalsIgnoreCase(MapUtils.getString(firstStore, Store.SN))) {
            return;
        }
        if (Objects.nonNull(lklV3ShopTermBiz.shopContract(store.getSn()))) {
            return;
        }
        Tuple2<Integer, Long> dependId = lklV3ShopTermBiz.merchantContract(merchantSn);
        if (dependId == null) {
            log.error("未找到商户入网子任务: {}", merchantSn);
            return;
        }
        createAddShopTask(merchant, store.getSn(), dependId);
    }

    public void do259StoreBind(String storeSn) {
        StoreInfo store = storeService.getStoreBySn(storeSn, devCode);
        if (Objects.isNull(store)) {
            return;
        }
        String merchantId = store.getMerchant_id();
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, devCode);
        String merchantSn = merchant.getSn();
        //1,当前商户报过的所有收单机构
        final List<Integer> providerList = providerTerminalBiz.getProviderList(merchantSn);
        //根据不同provider处理
        providerList.parallelStream().filter(provider -> !Objects.equals(provider, ProviderEnum.PROVIDER_LAKALA_V3.getValue())).forEach(provider -> {
            BasicProvider handleProvider = providerFactory.getProvider(String.valueOf(provider));
            if (handleProvider == null) {
                log.info("商户ID:{},provider;{},未找到处理类", merchantId, provider);
                return;
            }
            //处理绑定
            handleProvider.handleSqbStoreTerminal(storeSn, merchantSn, provider);
        });
    }

    /**
     * 给商户下所有门店创建对应拉卡拉网点
     *
     * @param merchantSn
     */
    public void syncLklStore(String merchantSn) {
        syncLklStore(merchantSn, null);
    }

    /**
     * 给商户下指定门店创建对应拉卡拉网点
     *
     * @param merchantSn
     */
    public void syncLklStore(String merchantSn, String sn) {
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, devCode);
        final List<Map> records;
        if (StringUtils.isEmpty(sn)) {
            PageInfo pageInfo = new PageInfo(1, 800);
            final ListResult listResult = cStoreService.getSimpleStoreListByMerchantId(merchant.getId(), pageInfo);
            records = listResult.getRecords();
            Set<String> storeSnSet = records.stream()
                    .map(record -> BeanUtil.getPropString(record, "sn"))
                    .collect(Collectors.toSet());
            log.info("syncLklStore商户号:{},门店记录sn:{}", merchantSn, JSONObject.toJSONString(storeSnSet));
        } else {
            records = Lists.newArrayList(cStoreService.getStoreByStoreSn(sn));
        }
        records.stream().forEach(storeMap -> {
            final String storeSn = BeanUtil.getPropString(storeMap, "sn");
            StoreInfo store = storeService.getStoreBySn(storeSn, devCode);
            if (Objects.isNull(store)) {
                return;
            }
            String merchantId = store.getMerchant_id();
            List<AcquirerMerchantDto> acquirerList = merchantProviderParamsService.getAcquirerMerchantInfo(merchantId);
            //是否成功进件拉卡拉
            boolean match = acquirerList.parallelStream().anyMatch(info -> info.getAcquirer().contains(AcquirerTypeEnum.LKL_V3.getValue()));
            if (!match) {
                log.info("syncLklStore商户号{},没有成功进件拉卡拉", merchantSn);
                return;
            }
            final Tuple2<Integer, Long> shopContract = lklV3ShopTermBiz.shopContract(store.getSn());
            // 等待审核也要看任务是否失败,如果失败要删除 lkl_v3_shop_term表,然后重新生成任务,如果真的是在审核中则不需要处理
            if (Objects.nonNull(shopContract) && Objects.equals(shopContract.get_1(), 0)) {
                ContractSubTask subTask = contractSubTaskMapper.selectByPrimaryKey(shopContract.get_2());
                if (subTask != null && ContractSubTaskProcessStatusEnum.PROCESS_FAIL.getValue().equals(subTask.getStatus())) {
                    log.info("syncLklStore商户号:{},门店号:{},id{},拉卡拉审核失败,删除拉卡拉门店信息", merchantSn, storeSn);
                    lklV3ShopTermBiz.deleteLklV3ShopTerm(store.getSn());
                }else{
                    log.info("syncLklStore商户号:{},门店号:{},已经创建门店等待拉卡拉审核", merchantSn, storeSn);
                    return;
                }
            }

            if (Objects.nonNull(shopContract) && Objects.equals(shopContract.get_1(), 1)) {
                log.info("syncLklStore商户号:{},门店号:{},已经存在拉卡拉门店信息", merchantSn, storeSn);
                return;
            }
            Tuple2<Integer, Long> dependId = lklV3ShopTermBiz.merchantContract(merchantSn);
            createAddShopTask(merchant, store.getSn(), dependId);
        });

    }

    private void createAddShopTask(MerchantInfo merchant, String storeSn, Tuple2<Integer, Long> dependId) {
        String merchantSn = merchant.getSn();
        String merchantId = merchant.getId();
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        MerchantProviderParams brandAcquirerParams = null;
        BrandMerchantInfoQueryResp brandMerchantInfoQueryResp = brandBusinessClient.getBrandMerchantInfoByMerchantId(merchantId);
        if (brandMerchantInfoQueryResp.isSubBrandMerchant() && brandMerchantInfoQueryResp.isBrandPayMode()) {
            BrandDetailInfoQueryResp brandDetailInfoQueryResp = brandBusinessClient.getBrandDetailInfoByBrandId(brandMerchantInfoQueryResp.getBrandId());
            String brandMainMerchantSn = brandDetailInfoQueryResp.getMainMerchantSn();
            brandAcquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(brandMainMerchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        }
        Map context = CollectionUtil.hashMap(CommonModel.STORE_SN, storeSn,
                ParamContextBiz.MERCHANT_FEE_RATES, lklV3Provider.getFeeRate(merchantId),
                ParamContextBiz.PAY_MERCHANT_ID, acquirerParams.getPay_merchant_id(),
                ParamContextBiz.BRAND_PAY_MERCHANT_ID, Objects.nonNull(brandAcquirerParams) ? brandAcquirerParams.getPay_merchant_id() : null
        );
        ContractTask task = new ContractTask()
                .setMerchant_sn(merchantSn)
                .setMerchant_name(merchant.getName())
                .setStatus(TaskStatus.PENDING.getVal())
                .setRule_group_id(AcquirerTypeEnum.LKL_V3.getValue())
                .setType(ProviderUtil.CONTRACT_TYPE_ADDTERM)
                .setAffect_status_success_task_count(0)
                .setAffect_sub_task_count(Objects.nonNull(brandAcquirerParams) ? 2 : 1)
                .setEvent_context(JSON.toJSONString(context));
        contractTaskBiz.insert(task);
        ContractSubTask update = new ContractSubTask()
                .setStatus_influ_p_task(1)
                .setChannel(ProviderUtil.LKL_V3_PROVIDER_CHANNEL)
                .setMerchant_sn(merchantSn)
                .setSchedule_dep_task_id(Objects.isNull(dependId) ? null : dependId.get_2())
                .setSchedule_status(Objects.isNull(dependId) ? 1 : dependId.get_1())
                .setContract_rule(McConstant.RULE_GROUP_LKLV3)
                .setRule_group_id(McConstant.RULE_GROUP_LKLV3)
                .setP_task_id(task.getId())
                .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_ADD_SHOP);
        contractSubTaskMapper.insert(update);
        lklV3ShopTermBiz.addShop(merchantSn, storeSn, acquirerParams.getPay_merchant_id(), update.getId());
        if (Objects.nonNull(brandAcquirerParams)) {
            ContractSubTask brand = new ContractSubTask()
                    .setStatus_influ_p_task(1)
                    .setChannel(ProviderUtil.LKL_V3_PROVIDER_CHANNEL)
                    .setMerchant_sn(merchantSn)
                    .setSchedule_dep_task_id(Objects.isNull(dependId) ? null : dependId.get_2())
                    .setSchedule_status(Objects.isNull(dependId) ? 1 : dependId.get_1())
                    .setContract_rule(McConstant.RULE_GROUP_LKLV3)
                    .setRule_group_id(McConstant.RULE_GROUP_LKLV3)
                    .setP_task_id(task.getId())
                    .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_BRAND_ADD_SHOP);
            contractSubTaskMapper.insert(brand);
            lklV3ShopTermBiz.addShop(merchantSn, storeSn, brandAcquirerParams.getPay_merchant_id(), update.getId());
        }
    }


}