package com.wosai.upay.job.util.bcs;

import lombok.Getter;

@Getter
public enum BcsCheckProcessEnum {

    UNSUBMIT("UNSUBMIT", "待提交"),

    HEADDISTRIBUT("HEADDISTRIBUT", "待总行分配"),

    BRANCHDISTRIBUT("BRANCHDISTRIBUT", "待分支行分配"),

    UNVERIFY("UNVERIFY", "待核实（地推人员）"),

    UNCHECK("UNCHECK", "待客户经理审核"),

    CHECKED("CHECKED", "待初审"),

    UNREVIEW("UNREVIEW", "待复审"),

    DOUBLE("DOUBLE","待双审"),

    PASS("PASS", "通过"),

    CANCLE("CANCLE", "作废（仅提交人有此权限）");

    private final String code;
    private final String description;

    BcsCheckProcessEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static BcsCheckProcessEnum fromCode(String code) {
        for (BcsCheckProcessEnum status : BcsCheckProcessEnum.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的业务状态码: " + code);
    }
}
