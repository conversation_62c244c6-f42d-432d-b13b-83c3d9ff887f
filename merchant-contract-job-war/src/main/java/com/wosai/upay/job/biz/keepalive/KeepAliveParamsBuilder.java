package com.wosai.upay.job.biz.keepalive;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.AcquirerOrgTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.bank.entity.FeeRateSnapshot;
import com.wosai.trade.service.result.FeeRateResponse;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.biz.AgentAppidBiz;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.dao.McAcquirerChangeDAO;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.model.entity.ContractStatusDO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerChangeDO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.xxljob.direct.keepalive.KeepAliveTaskStartJobHandler;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.wosai.upay.job.biz.acquirer.AbstractAcquirerChangeBiz.INDIRECT_COMBO_SNAPSHOT;

/**
 * 保活任务参数构建器
 * 负责构建保活任务所需的完整参数信息，包括费率、交易参数等
 *
 * <AUTHOR>
 * @date 2025/8/26
 */
@Slf4j
@Component
public class KeepAliveParamsBuilder {

    @Autowired
    private FeeRateService feeRateService;
    @Autowired
    private McAcquirerDAO mcAcquirerDAO;
    @Autowired
    private ContractStatusDAO contractStatusDAO;
    @Autowired
    private McAcquirerChangeDAO mcAcquirerChangeDAO;
    @Autowired
    private AgentAppidBiz agentAppidBiz;

    private static final SerializeConfig config;

    static {
        config = new SerializeConfig();
        config.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;
    }

    /**
     * 从收单机构切换快照构建费率信息
     * 如果快照中没有找到对应payway的费率，则回退到当前有效费率
     *
     * @param keepAliveParams    保活参数
     * @param mcAcquirerChangeDO 收单机构切换申请
     */
    public void buildFeeRateFromSnapshot(KeepAliveTaskStartJobHandler.KeepAliveParams keepAliveParams, McAcquirerChangeDO mcAcquirerChangeDO) {
        String merchantSn = keepAliveParams.getMerchant_sn();
        Integer payway = keepAliveParams.getPayway();

        log.info("开始从快照构建费率，商户: {}, payway: {}", merchantSn, payway);

        try {
            Map extra = CommonUtil.string2Map(mcAcquirerChangeDO.getExtra());
            List<FeeRateSnapshot> snapshotList = (List<FeeRateSnapshot>) WosaiMapUtils.getObject(extra, INDIRECT_COMBO_SNAPSHOT);

            if (WosaiCollectionUtils.isEmpty(snapshotList)) {
                log.warn("快照费率列表为空，回退到当前费率，商户: {}, payway: {}", merchantSn, payway);
                buildFeeRateFromCurrent(keepAliveParams);
                return;
            }

            Optional<FeeRateSnapshot> feeRateSnapshot = snapshotList.stream()
                    .filter(r -> r.getPayWay().equals(payway))
                    .findFirst();

            if (!feeRateSnapshot.isPresent()) {
                log.warn("快照中未找到payway: {} 费率，回退到当前费率，商户: {}", payway, merchantSn);
                buildFeeRateFromCurrent(keepAliveParams);
                return;
            }

            // 从快照费率ID获取详细费率信息
            FeeRateResponse feeRateResponse = feeRateService.getMerchantFeeRateById(feeRateSnapshot.get().getFeeRateId());

            if (feeRateResponse == null) {
                log.warn("费率ID: {} 获取详情失败，回退到当前费率，商户: {}, payway: {}",
                        feeRateSnapshot.get().getFeeRateId(), merchantSn, payway);
                buildFeeRateFromCurrent(keepAliveParams);
                return;
            }

            // 设置费率信息
            setFeeRateToParams(keepAliveParams, feeRateResponse);

            log.info("快照费率构建成功，商户: {}, payway: {}", merchantSn, payway);

        } catch (Exception e) {
            log.error("快照费率构建失败，回退到当前费率，商户: {}, payway: {}", merchantSn, payway, e);
            try {
                buildFeeRateFromCurrent(keepAliveParams);
            } catch (Exception fallbackException) {
                log.error("当前费率构建也失败，商户: {}, payway: {}", merchantSn, payway, fallbackException);
                throw new ContractBizException(String.format(
                        "费率构建失败，payway: %d，商户: %s", payway, merchantSn), fallbackException);
            }
        }
    }

    /**
     * 从当前有效费率构建费率信息
     *
     * @param keepAliveParams 保活参数
     */
    public void buildFeeRateFromCurrent(KeepAliveTaskStartJobHandler.KeepAliveParams keepAliveParams) {
        String merchantSn = keepAliveParams.getMerchant_sn();
        Integer payway = keepAliveParams.getPayway();

        log.info("开始从当前费率构建，商户: {}, payway: {}", merchantSn, payway);

        try {
            List<ListMchFeeRateResult> mchFeeRateResults = feeRateService.listMchEffectFeeRates(merchantSn);

            if (WosaiCollectionUtils.isEmpty(mchFeeRateResults)) {
                log.error("商户无有效费率，商户: {}", merchantSn);
                throw new ContractBizException(String.format("商户 %s 无有效费率", merchantSn));
            }

            Optional<ListMchFeeRateResult> effectFeeRate = mchFeeRateResults.stream()
                    .filter(r -> Objects.equals(payway, r.getPayWay()))
                    .findFirst();

            if (!effectFeeRate.isPresent()) {
                log.error("未找到payway: {} 费率，商户: {}", payway, merchantSn);
                throw new ContractBizException(String.format("未找到payway: %d 费率，商户: %s", payway, merchantSn));
            }

            // 设置费率信息
            setFeeRateToParams(keepAliveParams, effectFeeRate.get());

            log.info("当前费率构建成功，商户: {}, payway: {}", merchantSn, payway);

        } catch (Exception e) {
            log.error("当前费率构建失败，商户: {}, payway: {}", merchantSn, payway, e);
            throw e;
        }
    }

    /**
     * 设置费率信息到保活参数（FeeRateResponse版本）
     */
    private void setFeeRateToParams(KeepAliveTaskStartJobHandler.KeepAliveParams keepAliveParams, FeeRateResponse feeRateResponse) {
        // 设置渠道阶梯费率
        if (WosaiCollectionUtils.isNotEmpty(feeRateResponse.getChannelLadderFeeRates())) {
            Map<String, List<Map<String, Object>>> feeRates = new HashMap<>();
            for (FeeRateResponse.ChannelLadderFeeRate channelLadderFeeRate : feeRateResponse.getChannelLadderFeeRates()) {
                feeRates.put(channelLadderFeeRate.getType(),
                        JSON.parseObject(JSON.toJSONString(channelLadderFeeRate.getLadderFeeRates(), config),
                                new TypeReference<List<Map<String, Object>>>() {
                                }));
            }
            keepAliveParams.setChannel_ladder_fee_rates(feeRates);
        }

        // 设置渠道费率
        if (WosaiCollectionUtils.isNotEmpty(feeRateResponse.getChannelFeeRates())) {
            List<FeeRateResponse.ChannelFeeRate> channelFeeRates = feeRateResponse.getChannelFeeRates();
            keepAliveParams.setChannel_fee_rates(JSON.parseObject(JSON.toJSONString(channelFeeRates, config),
                    new TypeReference<List<Map<String, Object>>>() {
                    }));
        }

        // 设置阶梯费率
        if (WosaiCollectionUtils.isNotEmpty(feeRateResponse.getLadderFeeRates())) {
            List<FeeRateResponse.LadderFeeRate> ladderFeeRates = feeRateResponse.getLadderFeeRates();
            keepAliveParams.setLadder_fee_rates(JSON.parseObject(JSON.toJSONString(ladderFeeRates, config),
                    new TypeReference<List<Map<String, Object>>>() {
                    }));
        }

        // 设置固定费率
        keepAliveParams.setFee_rate(feeRateResponse.getFixedFeeRate());
    }

    /**
     * 设置费率信息到保活参数（ListMchFeeRateResult版本）
     */
    private void setFeeRateToParams(KeepAliveTaskStartJobHandler.KeepAliveParams keepAliveParams, ListMchFeeRateResult listMchFeeRateResult) {
        // 设置渠道阶梯费率
        if (WosaiCollectionUtils.isNotEmpty(listMchFeeRateResult.getChannelLadderFeeRates())) {
            List<ListMchFeeRateResult.ChannelLadderFeeRate> channelLadderFeeRates = listMchFeeRateResult.getChannelLadderFeeRates();
            Map<String, List<Map<String, Object>>> feeRates = new HashMap<>();
            for (ListMchFeeRateResult.ChannelLadderFeeRate channelLadderFeeRate : channelLadderFeeRates) {
                feeRates.put(channelLadderFeeRate.getType(),
                        JSON.parseObject(JSON.toJSONString(channelLadderFeeRate.getLadderFeeRate(), config),
                                new TypeReference<List<Map<String, Object>>>() {
                                }));
            }
            keepAliveParams.setChannel_ladder_fee_rates(feeRates);
        }

        // 设置渠道费率
        if (WosaiCollectionUtils.isNotEmpty(listMchFeeRateResult.getChannelFeeRates())) {
            List<ListMchFeeRateResult.ChannelFeeRate> channelFeeRates = listMchFeeRateResult.getChannelFeeRates();
            keepAliveParams.setChannel_fee_rates(JSON.parseObject(JSON.toJSONString(channelFeeRates, config),
                    new TypeReference<List<Map<String, Object>>>() {
                    }));
        }

        // 设置阶梯费率
        if (WosaiCollectionUtils.isNotEmpty(listMchFeeRateResult.getLadderFeeRates())) {
            List<ListMchFeeRateResult.LadderFeeRate> ladderFeeRates = listMchFeeRateResult.getLadderFeeRates();
            keepAliveParams.setLadder_fee_rates(JSON.parseObject(JSON.toJSONString(ladderFeeRates, config),
                    new TypeReference<List<Map<String, Object>>>() {
                    }));
        }

        // 设置基础费率
        keepAliveParams.setFee_rate(listMchFeeRateResult.getBscFeeRate());
    }

    /**
     * 构建完整的保活参数
     *
     * @param merchantProviderParamsDO 商户提供商参数
     * @return 保活参数
     */
    public KeepAliveTaskStartJobHandler.KeepAliveParams buildKeepAliveParams(MerchantProviderParamsDO merchantProviderParamsDO) {
        KeepAliveTaskStartJobHandler.KeepAliveParams keepAliveParams = new KeepAliveTaskStartJobHandler.KeepAliveParams();
        keepAliveParams.setMerchant_sn(merchantProviderParamsDO.getMerchantSn());
        keepAliveParams.setProvider(merchantProviderParamsDO.getProvider());
        keepAliveParams.setPayway(merchantProviderParamsDO.getPayway());

        // 构建交易参数
        buildTradeParams(keepAliveParams, merchantProviderParamsDO);

        // 构建费率信息
        buildFeeRateInfo(keepAliveParams);

        return keepAliveParams;
    }

    /**
     * 构建交易参数
     */
    private void buildTradeParams(KeepAliveTaskStartJobHandler.KeepAliveParams keepAliveParams, MerchantProviderParamsDO merchantProviderParamsDO) {
        // 设置代理商名称
        String agentName = agentAppidBiz.getAgentName(merchantProviderParamsDO.getPayway(),
                merchantProviderParamsDO.getProvider(), merchantProviderParamsDO.getChannelNo(), WosaiStringUtils.EMPTY);
        keepAliveParams.setB2c_agent_name(agentName);
        keepAliveParams.setC2b_agent_name(agentName);
        keepAliveParams.setWap_agent_name(agentName);
        keepAliveParams.setMini_agent_name(agentName);

        // 构建具体的交易参数
        Map<String, Object> tradeParams = buildSpecificTradeParams(merchantProviderParamsDO);
        keepAliveParams.setParams(tradeParams);
    }

    /**
     * 构建具体的交易参数
     */
    private Map<String, Object> buildSpecificTradeParams(MerchantProviderParamsDO providerParamsDO) {
        if (ProviderEnum.PROVIDER_LKLORG.getValue().equals(providerParamsDO.getProvider())) {
            if (Objects.equals(providerParamsDO.getPayway(), PaywayEnum.ALIPAY.getValue())) {
                Map<String, Object> params = new HashMap<>();
                params.put(TransactionParam.UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID, providerParamsDO.getPayMerchantId());
                params.put(TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerParamsDO.getProviderMerchantId());
                params.put(TransactionParam.TRADE_PARAMS_MERCHANT_NAME, providerParamsDO.getMerchantName());
                return params;
            }
            if (Objects.equals(providerParamsDO.getPayway(), PaywayEnum.WEIXIN.getValue())) {
                Map<String, Object> params = new HashMap<>();
                params.put(TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_MCH_ID, providerParamsDO.getPayMerchantId());
                params.put(TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerParamsDO.getProviderMerchantId());
                params.put(TransactionParam.TRADE_PARAMS_MERCHANT_NAME, providerParamsDO.getMerchantName());
                return params;
            }
            throw new ContractBizException("不支持的支付方式");
        } else if (ProviderEnum.PROVIDER_HAIKE.getValue().equals(providerParamsDO.getProvider())) {
            if (Objects.equals(providerParamsDO.getPayway(), PaywayEnum.ALIPAY.getValue())) {
                Map<String, Object> params = new HashMap<>();
                params.put(TransactionParam.UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID, providerParamsDO.getPayMerchantId());
                params.put(TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerParamsDO.getProviderMerchantId());
                params.put(TransactionParam.TRADE_PARAMS_MERCHANT_NAME, providerParamsDO.getMerchantName());
                return params;
            }
            if (Objects.equals(providerParamsDO.getPayway(), PaywayEnum.WEIXIN.getValue())) {
                Map<String, Object> params = new HashMap<>();
                params.put(TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_MCH_ID, providerParamsDO.getPayMerchantId());
                params.put(TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerParamsDO.getProviderMerchantId());
                params.put(TransactionParam.TRADE_PARAMS_MERCHANT_NAME, providerParamsDO.getMerchantName());
                return params;
            }
            throw new ContractBizException("不支持的支付方式");
        } else if (ProviderEnum.PROVIDER_FUYOU.getValue().equals(providerParamsDO.getProvider())) {
            if (Objects.equals(providerParamsDO.getPayway(), PaywayEnum.ALIPAY.getValue())) {
                Map<String, Object> params = new HashMap<>();
                params.put(TransactionParam.UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID, providerParamsDO.getPayMerchantId());
                params.put(TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerParamsDO.getProviderMerchantId());
                return params;
            }
            if (Objects.equals(providerParamsDO.getPayway(), PaywayEnum.WEIXIN.getValue())) {
                Map<String, Object> params = new HashMap<>();
                params.put(TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_MCH_ID, providerParamsDO.getPayMerchantId());
                params.put(TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerParamsDO.getProviderMerchantId());
                return params;
            }
            throw new ContractBizException("不支持的支付方式");
        } else {
            throw new ContractBizException("不支持的收单机构");
        }
    }

    /**
     * 构建费率信息
     */
    private void buildFeeRateInfo(KeepAliveTaskStartJobHandler.KeepAliveParams keepAliveParams) {
        // 费率信息 如果当前收单机构不是三方，去查询收单机构切换任务里面的快照信息。否则获取当前费率信息
        Optional<ContractStatusDO> contractStatusDO = contractStatusDAO.getByMerchantSn(keepAliveParams.getMerchant_sn());
        if (!contractStatusDO.isPresent()) {
            throw new ContractBizException("未进件成功");
        }
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(contractStatusDO.get().getAcquirer());
        if (!mcAcquirerDO.getType().equals(AcquirerOrgTypeEnum.THIRD_PARTY.getValue())) {
            Optional<McAcquirerChangeDO> mcAcquirerChangeDO = mcAcquirerChangeDAO.getLatestHasIndirectComboSnapshotSuccessApply(keepAliveParams.getMerchant_sn());
            if (!mcAcquirerChangeDO.isPresent()) {
                buildFeeRateFromCurrent(keepAliveParams);
            } else {
                buildFeeRateFromSnapshot(keepAliveParams, mcAcquirerChangeDO.get());
            }
        } else {
            buildFeeRateFromCurrent(keepAliveParams);
        }
    }


}
