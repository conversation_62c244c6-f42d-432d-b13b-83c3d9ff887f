package com.wosai.upay.job.xxljob.direct.tonglianv2;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.constant.DevCodeConstants;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyStatusEnum;
import com.wosai.upay.job.externalservice.aop.AopClient;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyLogDetailBO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.job.refactor.model.enums.PayBizApplyOperationEnum;
import com.wosai.upay.job.xxljob.context.TonglianV2Context;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.TongLianV2Param;
import com.wosai.upay.merchant.contract.model.tlV2.request.ElectUrlQueryRequest;
import com.wosai.upay.merchant.contract.model.tlV2.response.ElectSignStatusQueryResponse;
import com.wosai.upay.merchant.contract.model.tlV2.response.ElectUrlQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 通联收银宝V2签约状态查询定时任务
 *
 * <AUTHOR>
 * @date 2025/9/15
 */
@Slf4j
@Component("TonglianV2SignStatusJobHandler")
public class TonglianV2SignStatusJobHandler extends AbstractTonglianV2JobHandler {

    @Autowired
    private AopClient aopClient;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    /**
     * 签约链接有效期（毫秒）
     */
    private static final int SIGN_URL_VALID_MILLISECONDS = 7 * 24 * 60 * 60 * 1000;


    @Override
    public String getLockKey() {
        return "TonglianV2SignStatusJobHandler";
    }

    @Override
    protected List<PayBizApplyDO> queryPendingApplies(DirectJobParam param) {
        return queryWaitingSignApplies(param);
    }

    @Override
    protected void processApply(TonglianV2Context context) {
        processSignStatusCheck(context);
    }

    @Override
    protected String getJobName() {
        return "TonglianV2SignStatusJobHandler";
    }

    /**
     * 查询待签约的申请记录
     */
    private List<PayBizApplyDO> queryWaitingSignApplies(DirectJobParam param) {
        // 查询待法人签约和待结算人签约的申请记录，按priority正序排列
        Long queryTime = param.getQueryTime();
        LocalDateTime start = LocalDateTime.now().minusSeconds(queryTime);
        return payBizApplyDAO.selectWaitingSignApplies(DevCodeConstants.TONGLIAN_V2_DEV_CODE, start, param.getBatchSize());
    }

    /**
     * 处理签约状态检查
     */
    private void processSignStatusCheck(TonglianV2Context context) {
        PayBizApplyDO apply = context.getApply();
        log.info("开始处理申请单签约状态检查，applyId: {}, merchantSn: {}, detailStatus: {}",
                apply.getId(), apply.getMerchantSn(), apply.getDetailStatus());

        try {
            // 前置校验：获取商户参数和通联参数
            if (!validateAndPrepareContext(context)) {
                log.warn("前置校验失败，跳过处理，applyId: {}", apply.getId());
                delayPriority(apply);
                return;
            }

            // 1. 检查签约链接是否存在，不存在则查询并保存
            if (!ensureSignUrlExists(context)) {
                log.warn("获取签约链接失败，跳过处理，applyId: {}", apply.getId());
                return;
            }

            // 2. 判断是否已经失效，如果已经失效则修改状态为失效
            if (checkAndHandleSignUrlExpired(apply)) {
                log.info("签约链接已失效，applyId: {}", apply.getId());
                return;
            }

            // 3. 调用通联接口查询签约状态
            handleSignStatusQuery(context);

        } catch (Exception e) {
            log.error("处理申请单签约状态检查异常，applyId: {}", apply.getId(), e);
            // 推迟处理时间
            delayPriority(apply);
        }
    }

    private boolean ensureSignUrlExists(TonglianV2Context context) {
        PayBizApplyDO apply = context.getApply();
        if (StringUtils.hasText(apply.getSignUrl())) {
            return true;
        }
        log.info("签约链接不存在，开始查询，applyId: {}, detailStatus: {}", apply.getId(), apply.getDetailStatus());
        String signUrl = getSignUrl(context);
        if (WosaiStringUtils.isNotEmpty(signUrl)) {
            boolean isSettlementSign = PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode().equals(apply.getDetailStatus());
            if (isSettlementSign) {
                payBizApplyDAO.saveSettlementSignUrl(apply, signUrl);
                aopClient.sendTonglianV2SettlementSign(apply.getMerchantSn(), CollectionUtil.hashMap("contract_url", signUrl.replace("https://", "")));
            } else {
                payBizApplyDAO.saveLegalSignUrl(apply, signUrl);
                aopClient.sendTonglianV2LegalSign(apply.getMerchantSn(), CollectionUtil.hashMap("contract_url", signUrl.replace("https://", "")));
            }
            return true;
        }
        return false;
    }

    /**
     * 确保签约链接存在
     */
    private String getSignUrl(TonglianV2Context context) {
        PayBizApplyDO apply = context.getApply();
        try {
            ContractResponse contractResponse = queryElectUrl(context);
            if (!contractResponse.isSuccess()) {
                log.error("查询签约链接失败，applyId: {}, response: {}", apply.getId(), contractResponse.getMessage());
                return null;
            }

            ElectUrlQueryResponse queryResponse = JSON.parseObject(
                    JSON.toJSONString(contractResponse.getResponseParam()), ElectUrlQueryResponse.class);
            String url = queryResponse.getSybsignurl();

            if (StringUtils.hasText(url)) {
                log.info("成功查询并保存签约链接，applyId: {}", apply.getId());
                return url;
            } else {
                log.warn("查询到的签约链接为空，applyId: {}", apply.getId());
                return null;
            }

        } catch (Exception e) {
            log.error("查询签约链接异常，applyId: {}", apply.getId(), e);
            return null;
        }
    }

    /**
     * 检查并处理签约链接失效
     */
    private boolean checkAndHandleSignUrlExpired(PayBizApplyDO apply) {
        Long signUrlTime = apply.getSignUrlTime();
        boolean expired = System.currentTimeMillis() - signUrlTime > SIGN_URL_VALID_MILLISECONDS;
        if (expired) {
            log.info("签约链接已失效，更新状态，applyId: {}, detailStatus: {}", apply.getId(), apply.getDetailStatus());
            payBizApplyDAO.updateToExpired(apply);
            // 记录操作日志
            payBizApplyLogDAO.logOperationWithJsonDetail(
                    String.valueOf(apply.getId()),
                    PayBizApplyOperationEnum.SIGN_URL_EXPIRED,
                    "SYSTEM",
                    "SYSTEM",
                    "SYSTEM",
                    PayBizApplyLogDetailBO.create(PayBizApplyStatusEnum.PROCESSING, apply.getDetailStatusEnum()));
            return true;
        }

        return false;
    }

    /**
     * 处理签约状态查询
     */
    private void handleSignStatusQuery(TonglianV2Context context) {
        PayBizApplyDO apply = context.getApply();
        log.info("开始查询签约状态，applyId: {}, merchantSn: {}", apply.getId(), apply.getMerchantSn());

        try {
            TongLianV2Param tongLianV2Param = context.getTongLianV2Param();

            // 查询签约状态
            ContractResponse contractResponse = tongLianV2Service.queryelectsignStatus(apply.getMerchantSn(), tongLianV2Param);
            if (!contractResponse.isSuccess()) {
                log.error("查询签约状态失败，applyId: {}, response: {}", apply.getId(), contractResponse.getMessage());
                delayPriority(apply);
                return;
            }

            ElectSignStatusQueryResponse statusResponse = JSON.parseObject(
                    JSON.toJSONString(contractResponse.getResponseParam()), ElectSignStatusQueryResponse.class);

            // 处理不同的签约状态
            handleSignStatusResponse(context, statusResponse);

        } catch (Exception e) {
            log.error("查询签约状态异常，applyId: {}", apply.getId(), e);
            delayPriority(apply);
        }
    }

    /**
     * 处理签约状态响应
     */
    private void handleSignStatusResponse(TonglianV2Context context, ElectSignStatusQueryResponse statusResponse) {
        PayBizApplyDO apply = context.getApply();
        // 协议状态 0：成功 1：失败 2：签约中 4：部分签约 9：未签约
        if (ElectSignStatusQueryResponse.ELECT_SIGN_STATUS_NOT_SIGN.equals(statusResponse.getElectsignstatus()) || ElectSignStatusQueryResponse.ELECT_SIGN_STATUS_SIGNING.equals(statusResponse.getElectsignstatus())) {
            delayPriority(apply);
        } else if (ElectSignStatusQueryResponse.ELECT_SIGN_STATUS_FAIL.equals(statusResponse.getElectsignstatus())) {
            // 签约失败
            transitionToSignFailed(apply);
        } else if (ElectSignStatusQueryResponse.ELECT_SIGN_STATUS_PARTIAL.equals(statusResponse.getElectsignstatus()) && apply.isDetailLegalSigning()) {
            // 部分签约 更新状态为待结算人签约 & 记录日志 & 查询结算人签约链接并保存，通过 aop发送
            transitionToSettlementSign(context);
        } else if (ElectSignStatusQueryResponse.ELECT_SIGN_STATUS_SUCCESS.equals(statusResponse.getElectsignstatus())) {
            // 签约成功，需要判断是否需要先经过结算人签约阶段
            if (apply.isDetailLegalSigning() && apply.getFlowInfoBO().hasSettlementSignNode()) {
                // 当前是法人签约中且需要结算人签约，先转到待结算人签约
                log.info("签约成功，但需要先经过结算人签约阶段，applyId: {}", apply.getId());
                transitionToSettlementSign(context);
                // 然后立即转到合规性审核
                transitionToComplianceAudit(apply);
            } else {
                // 直接转到合规性审核
                transitionToComplianceAudit(apply);
            }
        } else {
            delayPriority(apply);
        }
    }

    /**
     * 转换到待结算人签约状态
     */
    private void transitionToSettlementSign(TonglianV2Context context) {
        PayBizApplyDO apply = context.getApply();
        try {
            // 查询并保存结算人签约链接
            queryAndSaveSettlementSignUrl(context);
            // 更新状态为待结算人签约
            payBizApplyDAO.completeCurrentStageAndMoveToNext(apply);

            // 记录操作日志
            payBizApplyLogDAO.logOperationWithJsonDetail(
                    String.valueOf(apply.getId()),
                    PayBizApplyOperationEnum.LEGAL_SIGN_SUCCESS,
                    "SYSTEM",
                    "SYSTEM",
                    "SYSTEM",
                    PayBizApplyLogDetailBO.create(PayBizApplyStatusEnum.PROCESSING, PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN)
            );
            directStatusBiz.createOrUpdateDirectStatus(apply.getMerchantSn(), DevCodeConstants.TONGLIAN_V2_DEV_CODE, DirectStatus.STATUS_PROCESS, null);

        } catch (Exception e) {
            log.error("转换到待结算人签约状态失败，applyId: {}", apply.getId(), e);
        }
    }

    /**
     * 转换到合规性审核状态
     */
    private void transitionToComplianceAudit(PayBizApplyDO apply) {
        try {
            // 更新状态为合规性审核中
            payBizApplyDAO.completeCurrentStageAndMoveToNext(apply);

            // 记录操作日志
            payBizApplyLogDAO.logOperationWithJsonDetail(
                    String.valueOf(apply.getId()),
                    PayBizApplyOperationEnum.SIGN_SUCCESS,
                    "SYSTEM",
                    "SYSTEM",
                    "SYSTEM",
                    PayBizApplyLogDetailBO.create(PayBizApplyStatusEnum.PROCESSING, PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_AUDITING)
            );
            directStatusBiz.createOrUpdateDirectStatus(apply.getMerchantSn(), DevCodeConstants.TONGLIAN_V2_DEV_CODE, DirectStatus.STATUS_PROCESS, null);

        } catch (Exception e) {
            log.error("转换到合规性审核状态失败，applyId: {}", apply.getId(), e);
        }
    }

    /**
     * 转换到签约失败状态
     */
    private void transitionToSignFailed(PayBizApplyDO apply) {
        try {
            // 更新状态为签约失败
            payBizApplyDAO.updateToSignFailed(apply);
            // 记录操作日志
            payBizApplyLogDAO.logOperationWithJsonDetail(
                    String.valueOf(apply.getId()),
                    PayBizApplyOperationEnum.SIGN_FAILED,
                    "SYSTEM",
                    "SYSTEM",
                    "SYSTEM",
                    PayBizApplyLogDetailBO.create(PayBizApplyStatusEnum.PROCESSING, apply.getDetailStatusEnum())
            );
            directStatusBiz.createOrUpdateDirectStatus(apply.getMerchantSn(), DevCodeConstants.TONGLIAN_V2_DEV_CODE, DirectStatus.STATUS_PROCESS, "签约失败");

        } catch (Exception e) {
            log.error("转换到签约失败状态失败，applyId: {}", apply.getId(), e);
        }
    }

    /**
     * 查询并保存结算人签约链接
     */
    private void queryAndSaveSettlementSignUrl(TonglianV2Context context) {
        PayBizApplyDO apply = context.getApply();
        try {
            // 查询结算人签约链接
            ContractResponse contractResponse = queryElectUrl(context);
            if (!contractResponse.isSuccess()) {
                log.error("查询结算人签约链接失败，applyId: {}, response: {}", apply.getId(), contractResponse.getMessage());
                return;
            }

            ElectUrlQueryResponse queryResponse = JSON.parseObject(
                    JSON.toJSONString(contractResponse.getResponseParam()), ElectUrlQueryResponse.class);
            String url = queryResponse.getSybsignurl();

            if (StringUtils.hasText(url)) {
                // 保存结算人签约链接
                payBizApplyDAO.saveSettlementSignUrl(apply, url);
                aopClient.sendTonglianV2SettlementSign(apply.getMerchantSn(), CollectionUtil.hashMap("contract_url", url.replace("https://", "")));
                log.info("成功查询并保存结算人签约链接，applyId: {}", apply.getId());
            } else {
                log.warn("查询到的结算人签约链接为空，applyId: {}", apply.getId());
            }

        } catch (Exception e) {
            log.error("查询结算人签约链接异常，applyId: {}", apply.getId(), e);
        }
    }

    /**
     * 推迟优先级处理时间（智能延迟策略）
     */
    protected void delayPriority(PayBizApplyDO apply) {
        try {
            int delayMinutes = calculateDelayMinutes(apply);
            LocalDateTime newPriority = LocalDateTime.now().plusMinutes(delayMinutes);
            payBizApplyDAO.updatePriority(apply, newPriority);

            log.debug("推迟处理时间，applyId: {}, delayMinutes: {}, newPriority: {}",
                    apply.getId(), delayMinutes, newPriority);
        } catch (Exception e) {
            log.error("推迟处理时间失败，applyId: {}", apply.getId(), e);
        }
    }

    /**
     * 计算延迟分钟数（基于时间的智能延迟策略）
     * 第1-3天：5分钟 + 随机扰动
     * 第4-7天：30分钟 + 随机扰动
     */
    protected int calculateDelayMinutes(PayBizApplyDO apply) {
        LocalDateTime referenceTime = getReferenceTimeForDelay(apply);
        if (referenceTime == null) {
            // 如果没有参考时间，使用默认延迟
            return BASE_DELAY_MINUTES_1_3_DAYS + RANDOM.nextInt(RANDOM_DELAY_RANGE);
        }

        // 计算参考时间到现在的天数
        long daysSinceReference = ChronoUnit.DAYS.between(referenceTime, LocalDateTime.now());

        int baseDelay;
        if (daysSinceReference <= 3) {
            // 第1-3天：5分钟基础延迟
            baseDelay = BASE_DELAY_MINUTES_1_3_DAYS;
        } else if (daysSinceReference <= 7) {
            // 第4-7天：30分钟基础延迟
            baseDelay = BASE_DELAY_MINUTES_4_7_DAYS;
        } else {
            // 超过7天：使用较长延迟，避免频繁查询
            baseDelay = BASE_DELAY_MINUTES_4_7_DAYS * 2;
        }

        // 添加随机扰动，避免任务集中执行
        int randomDelay = RANDOM.nextInt(RANDOM_DELAY_RANGE);
        return baseDelay + randomDelay;
    }

    /**
     * 根据签约链接时间计算延迟分钟数
     * 第1-3天：5分钟 + 随机扰动
     * 第4-7天：30分钟 + 随机扰动
     */

    private LocalDateTime getReferenceTimeForDelay(PayBizApplyDO apply) {
        Long signUrlTime = apply.getSignUrlTime();
        if (signUrlTime == null) {
            // 如果没有签约链接时间，使用默认延迟
            return null;
        }
        return LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(signUrlTime),
                java.time.ZoneId.systemDefault());
    }

    private ContractResponse queryElectUrl(TonglianV2Context context) {
        PayBizApplyDO apply = context.getApply();
        ElectUrlQueryRequest request = new ElectUrlQueryRequest();
        request.setAcquirerMerchantId(context.getMerchantProviderParam().getPayMerchantId());

        // 根据详细状态确定签约类型
        boolean isSettlementSign = PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode().equals(apply.getDetailStatus());
        request.setType(isSettlementSign ? ElectUrlQueryRequest.SETTLE_ELECT_SIGN : ElectUrlQueryRequest.LEGAL_ELECT_SIGN);
        return tongLianV2Service.queryElectUrlV2(request, context.getTongLianV2Param());
    }

    /**
     * 判断是否需要结算人签约节点
     * 通联收银宝V2的业务规则：法人和结算人不是同一人时需要待结算人签约
     *
     * @param merchantSn 商户号
     * @return true-需要，false-不需要
     */
    private boolean needSettlementSignNode(String merchantSn) {
        try {
            java.util.Map merchant = merchantService.getMerchantBySn(merchantSn);
            java.util.Map merchantBankAccount = merchantService.getMerchantBankAccountByMerchantId(WosaiMapUtils.getString(merchant, DaoConstants.ID));
            MerchantBusinessLicenseInfo merchantBusinessLicenseInfo = merchantBusinessLicenseService.getLatestMerchantBusinessLicenseByMerchantId(WosaiMapUtils.getString(merchant, DaoConstants.ID));

            // 判断银行账户类型是否为对公账户(TYPE=1)，且法人身份证号与结算人身份证号不一致
            if (WosaiMapUtils.getIntValue(merchantBankAccount, MerchantBankAccount.TYPE) == 1) {
                return !WosaiStringUtils.equals(merchantBusinessLicenseInfo.getLegal_person_id_number(), WosaiMapUtils.getString(merchantBankAccount, MerchantBankAccount.IDENTITY));
            }
            return false;
        } catch (Exception e) {
            log.error("判断是否需要结算人签约节点异常，merchantSn: {}", merchantSn, e);
            // 异常情况下默认不需要结算人签约
            return false;
        }
    }

}