package com.wosai.upay.job.biz.payBizApply;

import com.wosai.upay.job.model.dto.request.PayBizApplyReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizApplyStatusReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizEnabledSetReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizSignResendReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizSignUrlValidateReqDTO;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.model.dto.response.PayBizApplyStatusDetailRspDTO;
import com.wosai.upay.job.model.dto.response.PayBizApplyStatusRspDTO;

/**
 * 支付业务开通申请处理服务接口
 * 根据dev_code找到对应的实现类，每个实现类自己去实现流程节点的初始化和自定义校验逻辑
 *
 * <AUTHOR>
public interface PayBizApplyHandleService {

    /**
     * 获取dev_code，为了根据dev_code找到对应的处理类
     *
     * @return dev_code
     */
    String getDevCode();

    /**
     * 获取业务名称
     *
     * @return 业务名称
     */
    String getBusinessName();

    /**
     * 处理支付业务申请
     *
     * @param request 申请请求
     * @return 申请结果
     */
    CuaCommonResultDTO handleApply(PayBizApplyReqDTO request);

    /**
     * 获取支付业务申请状态详情
     *
     * @param request 查询请求
     * @return 状态详情
     */
    PayBizApplyStatusDetailRspDTO getPayBizApplyStatusDetailInfo(PayBizApplyStatusReqDTO request);

    /**
     * 获取支付业务申请状态
     * @param request 查询请求
     * @return 状态
     */
    PayBizApplyStatusRspDTO getPayBizApplyStatusInfo(PayBizApplyStatusReqDTO request);

    /**
     * 设置支付业务是否启用
     * @param request 请求参数
     * @return 结果
     */
    CuaCommonResultDTO setPayBizEnabled(PayBizEnabledSetReqDTO request);
    
    /**
     * 重新发送签约链接
     * @param request 请求参数
     * @return 结果
     */
    CuaCommonResultDTO reSendSignUrl(PayBizSignResendReqDTO request);

    /**
     * 校验签约链接是否有效
     * @param request 请求参数
     * @return 校验结果，true表示校验通过（未过期），false表示校验失败（已过期）
     */
    CuaCommonResultDTO validateSignUrl(PayBizSignUrlValidateReqDTO request);
}
