package com.wosai.upay.job.externalservice.trademanage;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.trade.service.TradeComboService;
import com.wosai.trade.service.activity.constant.ActivityConstants;
import com.wosai.trade.service.activity.response.ApplyConditionQueryResponse;
import com.wosai.trade.service.activity.response.QuotaApplyConditionQueryResponse;
import com.wosai.trade.service.request.PageInfo;
import com.wosai.trade.service.result.ListResult;
import com.wosai.trade.service.result.QueryTradeComboByIdResult;
import com.wosai.upay.job.externalservice.trademanage.service.MyApplyActivityService;
import com.wosai.upay.job.externalservice.trademanage.service.MyQuotaApplyActivityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/9/23
 */
@Slf4j
@Component
public class TradeManageClient {

    @Autowired
    private MyApplyActivityService myApplyActivityService;
    @Autowired
    private MyQuotaApplyActivityService myQuotaApplyActivityService;
    @Autowired
    private TradeComboService tradeComboService;

    /**
     * 套餐信息缓存，缓存30分钟
     */
    private final Cache<Long, QueryTradeComboByIdResult> comboCache = CacheBuilder.newBuilder()
            .maximumSize(1000)  // 最大缓存1000个套餐
            .expireAfterWrite(5, TimeUnit.MINUTES)  // 写入后30分钟过期
            .build();

    public Optional<ApplyConditionQueryResponse> getLatestApply(String merchantSn, int status) {
        Map request = CollectionUtil.hashMap(
                "sn", merchantSn,
                "status", status,
                "page", 1,
                "page_size", 1,
                "order_by", Arrays.asList(
                        CollectionUtil.hashMap(
                                "field", "create_at",
                                "order", PageInfo.OrderBy.OrderType.DESC.getValue()
                        )
                )
        );

        ListResult<ApplyConditionQueryResponse> applyResponse = myApplyActivityService.conditionQuery(request);
        return WosaiCollectionUtils.isEmpty(applyResponse.getRecords()) ? Optional.empty() : Optional.of(applyResponse.getRecords().get(0));
    }

    public List<ApplyConditionQueryResponse> getEffectApplyByMerchantSn(String merchantSn) {
        Map request = CollectionUtil.hashMap(
                "sn", merchantSn,
                "status", ActivityConstants.EFFECT,
                "page", 1,
                "page_size", 100,
                "order_by", Collections.singletonList(
                        CollectionUtil.hashMap(
                                "field", "create_at",
                                "order", PageInfo.OrderBy.OrderType.DESC.getValue()
                        )
                )
        );

        ListResult<ApplyConditionQueryResponse> applyResponse = myApplyActivityService.conditionQuery(request);
        return applyResponse.getRecords();
    }

    public List<QuotaApplyConditionQueryResponse> getEffectQuotaByMerchantSn(String merchantSn) {
        Map request = CollectionUtil.hashMap(
                "sn", merchantSn,
                "status", ActivityConstants.EFFECT,
                "page", 1,
                "page_size", 100,
                "order_by", Collections.singletonList(
                        CollectionUtil.hashMap(
                                "field", "create_at",
                                "order", PageInfo.OrderBy.OrderType.DESC.getValue()
                        )
                )
        );

        ListResult<QuotaApplyConditionQueryResponse> quotaApplyConditionQueryResponseListResult = myQuotaApplyActivityService.conditionQuery(request);
        return quotaApplyConditionQueryResponseListResult.getRecords();
    }


    /**
     * 获取套餐信息，优先从缓存获取
     *
     * @param comboId 套餐ID
     * @return 套餐信息
     */
    public QueryTradeComboByIdResult getTradeComboById(Long comboId) {
        if (comboId == null) {
            return null;
        }

        try {
            return comboCache.get(comboId, () -> {
                log.debug("从远程服务获取套餐信息，comboId: {}", comboId);
                QueryTradeComboByIdResult result = tradeComboService.queryTradeComboById(comboId);
                if (result != null) {
                    log.debug("成功获取套餐信息，comboId: {}, name: {}", comboId, result.getName());
                } else {
                    log.warn("套餐信息不存在，comboId: {}", comboId);
                }
                return result;
            });
        } catch (Exception e) {
            log.error("获取套餐信息失败，comboId: {}", comboId, e);
            // 缓存异常时直接调用原始服务
            try {
                return tradeComboService.queryTradeComboById(comboId);
            } catch (Exception ex) {
                log.error("直接调用套餐服务也失败，comboId: {}", comboId, ex);
                return null;
            }
        }
    }

}
