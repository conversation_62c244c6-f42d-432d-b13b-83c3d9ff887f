package com.wosai.upay.job.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: lklV3的终端信息
 * <AUTHOR>
 * @Date 2021/4/23 11:09 上午
 **/
@Data
@Accessors(chain = true)
public class LklV3ShopTerm {

    private String id;

    @NotBlank(message = "商户号不能为空")
    @JSONField(name = "merchant_sn")
    private String merchantSn;

    @NotBlank(message = "门店号不能为空")
    @JsonProperty(value = "store_sn")
    private String storeSn;

    private String shopId;
    /**
     * 拉卡拉商户号
     */
    @JsonProperty(value = "mer_inner_no")
    private String merInnerNo;

    private String lkl_v3_term;

    public List<LklV3Term> getLklV3TermInfo() {
        if (Objects.isNull(lkl_v3_term)) {
            return null;
        }
        List list = JSON.parseObject(lkl_v3_term, List.class);
        return (List<LklV3Term>) list.stream().map(x -> JSONObject.parseObject(JSON.toJSONString(x), LklV3Term.class)).collect(Collectors.toList());
    }

    public LklV3ShopTerm setLklV3TermInfo(List<LklV3Term> lklV3Term) {
        lkl_v3_term = JSON.toJSONString(lklV3Term);
        return this;
    }

}