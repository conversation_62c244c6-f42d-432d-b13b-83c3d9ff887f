package com.wosai.upay.job.consumer;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.audit.merchant.events.ContractSupplAuditEvent;
import com.wosai.upay.job.constant.DevCodeConstants;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.PayBizApplyDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.merchant.audit.api.pojo.resp.ContractSupplAuditResultResDTO;
import com.wosai.upay.merchant.audit.api.service.ContractSupplAuditService;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.StringJoiner;

/**
 * OSP审核结果消费者
 * 处理通联V2收银宝的风控审核结果
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "consumer.init", havingValue = "true")
public class BusinessAuditConsumer extends AbstractDataBusConsumer {

    private static final String AUDIT_STATUS_PASS = "PASS";
    private static final String AUDIT_STATUS_REJECT = "REJECT";

    @Autowired
    private PayBizApplyDAO payBizApplyDAO;

    @Autowired
    private ContractSupplAuditService contractSupplAuditService;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    /**
     * 处理OSP业务审核结果
     *
     * @param record Kafka消费者记录
     */
    @KafkaListener(topics = "callback_OSP_business-audit", containerFactory = "businessAuditKafkaListenerContainerFactory")
    public void handleBusinessAudit(ConsumerRecord<String, GenericRecord> record) {
        handle(record);
    }

    @Override
    protected void doHandleEvent(AbstractEvent event) {
        if (!(event instanceof ContractSupplAuditEvent)) {
            return;
        }
        log.info("start handling callback_OSP_business-audit event : {}", JSON.toJSONString(event));

        ContractSupplAuditEvent contractSupplAuditEvent = (ContractSupplAuditEvent) event;

        // 只处理通联V2收银宝的审核事件
        if (!isTongLianV2Event(contractSupplAuditEvent)) {
            log.debug("非通联V2审核事件，跳过处理. merchantSn: {}, contractType: {}",
                    contractSupplAuditEvent.getMerchantSn(), contractSupplAuditEvent.getContractType());
            return;
        }

        processTongLianV2AuditEvent(contractSupplAuditEvent);
    }

    /**
     * 判断是否为通联V2审核事件
     *
     * @param event 审核事件
     * @return true-是通联V2事件，false-不是
     */
    private boolean isTongLianV2Event(ContractSupplAuditEvent event) {
        return event != null &&
                StringUtils.isNotBlank(event.getContractType()) &&
                AcquirerTypeEnum.TONG_LIAN_V2.getValue().equals(event.getContractType());
    }

    /**
     * 处理通联V2审核事件
     *
     * @param event 审核事件
     */
    private void processTongLianV2AuditEvent(ContractSupplAuditEvent event) {
        String merchantSn = event.getMerchantSn();
        String auditStatus = event.getAuditStatusSymbol();

        if (StringUtils.isBlank(merchantSn) || StringUtils.isBlank(auditStatus)) {
            log.warn("审核事件参数不完整. merchantSn: {}, auditStatus: {}", merchantSn, auditStatus);
            return;
        }

        // 查询对应的支付业务申请记录
        Optional<PayBizApplyDO> payBizApplyOpt = payBizApplyDAO.selectByMerchantSnAndDevCode(
                merchantSn, DevCodeConstants.TONGLIAN_V2_DEV_CODE);

        if (!payBizApplyOpt.isPresent()) {
            log.warn("未找到商户的通联V2支付业务申请记录. merchantSn: {}", merchantSn);
            return;
        }

        PayBizApplyDO payBizApply = payBizApplyOpt.get();

        // 只处理风控审核中的状态
        if (!payBizApply.isDetailRiskControlAuditing()) {
            log.debug("申请记录不在风控审核中状态，跳过处理. merchantSn: {}, detailStatus: {}",
                    merchantSn, payBizApply.getDetailStatus());
            return;
        }

        // 根据审核结果进行相应处理
        if (AUDIT_STATUS_PASS.equals(auditStatus)) {
            handleAuditPass(payBizApply);
        } else if (AUDIT_STATUS_REJECT.equals(auditStatus)) {
            handleAuditReject(payBizApply, event);
        } else {
            log.warn("不支持的审核状态. merchantSn: {}, auditStatus: {}", merchantSn, auditStatus);
        }
    }

    /**
     * 处理审核通过的情况
     *
     * @param payBizApply 支付业务申请记录
     */
    private void handleAuditPass(PayBizApplyDO payBizApply) {
        try {
            payBizApplyDAO.updateToWaitForSupplement(payBizApply);
            log.info("风控审核通过，状态更新为待补录. merchantSn: {}, applyId: {}",
                    payBizApply.getMerchantSn(), payBizApply.getId());
        } catch (Exception e) {
            log.error("更新待补录状态失败. merchantSn: {}, applyId: {}",
                    payBizApply.getMerchantSn(), payBizApply.getId(), e);
        }
    }

    /**
     * 处理审核驳回的情况
     *
     * @param payBizApply 支付业务申请记录
     * @param event       审核事件
     */
    private void handleAuditReject(PayBizApplyDO payBizApply, ContractSupplAuditEvent event) {
        String merchantSn = event.getMerchantSn();

        // 获取收单机构参数
        Optional<MerchantProviderParamsDO> acquirerParamsOpt = merchantProviderParamsDAO
                .getMerchantProviderParamsByProviderAndPayway(
                        merchantSn,
                        ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(),
                        PaywayEnum.ACQUIRER.getValue());

        if (!acquirerParamsOpt.isPresent()) {
            log.error("未找到通联收银宝相关参数. merchantSn: {}", merchantSn);
            return;
        }

        MerchantProviderParamsDO acquirerParams = acquirerParamsOpt.get();
        String payMerchantId = acquirerParams.getPayMerchantId();

        try {
            // 查询审核驳回原因
            ContractSupplAuditResultResDTO auditResult = contractSupplAuditService
                    .queryLastContractSupplAuditResultByProvider(
                            payMerchantId, AcquirerTypeEnum.TONG_LIAN_V2.getValue());

            // 构造驳回原因
            String rejectReason = buildRejectReason(auditResult);

            // 更新状态为风控审核驳回
            payBizApplyDAO.updateToRiskControlRejected(payBizApply, rejectReason);

            log.info("风控审核驳回，状态更新为风控审核驳回. merchantSn: {}, applyId: {}, reason: {}",
                    merchantSn, payBizApply.getId(), rejectReason);

        } catch (Exception e) {
            log.error("处理审核驳回失败. merchantSn: {}, applyId: {}",
                    merchantSn, payBizApply.getId(), e);
        }
    }

    /**
     * 构造审核驳回原因
     *
     * @param auditResult 审核结果
     * @return 驳回原因字符串
     */
    private String buildRejectReason(ContractSupplAuditResultResDTO auditResult) {
        if (auditResult == null || WosaiCollectionUtils.isEmpty(auditResult.getAuditTemplate())) {
            return "审核驳回";
        }

        StringJoiner reasonJoiner = new StringJoiner(",");
        auditResult.getAuditTemplate().forEach(template -> {
            if (template != null && StringUtils.isNotBlank(template.getContent())) {
                reasonJoiner.add(template.getContent());
            }
        });

        String reasons = reasonJoiner.toString();
        return StringUtils.isNotBlank(reasons) ? reasons : "审核驳回";
    }
}
