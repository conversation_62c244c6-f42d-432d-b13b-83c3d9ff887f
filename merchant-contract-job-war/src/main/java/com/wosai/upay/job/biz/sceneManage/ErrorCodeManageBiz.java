package com.wosai.upay.job.biz.sceneManage;

import cn.hutool.core.util.StrUtil;
import com.wosai.upay.job.model.ErrorInfo;
import com.wosai.upay.scene.SceneConfigFacade;
import com.wosai.upay.scene.service.activity.response.SceneConfigRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @Description: 对接错误码管理平台
 * <AUTHOR>
 * @Date 2024/6/6 10:34
 */
@Component
@Slf4j
public class ErrorCodeManageBiz {

    public static final String FALLBACK_COPY = "兜底文案";
    public static final String FALLBACK_COPY_CODE = "0000";
    public static final String GENERAL_ERROR_CODE = "9999";
    public static final String GENERAL_ERROR_MESSAGE = "未成功获取失败信息";
    public static final String SPA = "SP";
    public static final String CRM = "crm";
    public static final String APP = "app";
    public static final String CONSUMER = "consumer";
    public static final String SOURCE_MEMO = "source_memo";
    public static final String CONTRACT_MEMO = "contract_memo";
    public static final String CONTRACT_CODE = "contract_code";
    public static final String PLATFORM_LKL = "lkl";
    public static final String PLATFORM_WECHAT = "wechat";
    public static final String PLATFORM_WECHAT_SUB_DEV_CONFIG = "wechat_sub_dev_config";
    public static final String PLATFORM_ALY = "aly";
    public static final String PLATFORM_WECHAT_AUTH = "wechat_auth";
    public static final String PLATFORM_TONGLIAN = "tonglian";
    public static final String PLATFORM_TONGLIAN_V2 = "tonglianV2";
    public static final String PLATFORM_WEIXIN_DIRECT = "weixin_direct";
    public static final String PLATFORM_ALI_DIRECT = "ali_direct";
    public static final String PLATFORM_UMS = "ums";
    public static final String PLATFORM_PSBC = "psbc";
    public static final String PLATFORM_CCB = "ccb";
    public static final String PLATFORM_HXB = "hxb";
    public static final String PLATFORM_HAIKE = "haike";
    public static final String PLATFORM_CCB_DECP = "ccb_decp";
    public static final String PLATFORM_ACQUIRER_APPROVE = "acquirer_approve";
    public static final String PLATFORM_POS_ERROR_MESSAGE = "pos_error_message";
    public static final String PLATFORM_ONLINE_PAYMENT = "online_payment";
    public static final String PLATFORM_UNION_PAY = "union_pay";
    /**
     * 根据不同的收钱吧平台获取不同对接平台的提示文案
     *
     * @param viewEndpoint 文案要被哪端(crm,spa,app,消费者端)看到
     * @param originalMsg  原始错误信息
     * @param platform     原始错误信息来自哪一个对接平台
     * @return
     */
    public ErrorInfo getPromptMessageFromErrorCodeManager(String viewEndpoint, String originalMsg, String platform) {
        if (StringUtils.isEmpty(originalMsg)) {
            originalMsg = GENERAL_ERROR_MESSAGE;
        }
        final ErrorInfo errorInfo = new ErrorInfo();
        SceneConfigRecord sceneConfigRecord = getSceneConfigRecord(originalMsg, platform);
        if (Objects.isNull(sceneConfigRecord)) {
            errorInfo.setFallBack(true);
            sceneConfigRecord = getFirstNonNullSceneConfigRecord(platform);
            if (Objects.isNull(sceneConfigRecord)) {
                return errorInfo.setMsg(originalMsg).setCode(GENERAL_ERROR_CODE);
            }
        }

        String returnMsg = getReturnMessage(viewEndpoint, sceneConfigRecord, originalMsg);
        String code = sceneConfigRecord.getSceneErrorCode();
        String returnCode = StringUtils.isEmpty(code) ? FALLBACK_COPY_CODE : code;

        returnMsg = returnMsg.contains("#") ? returnMsg.replace("#", originalMsg) : returnMsg;
        return errorInfo.setMsg(String.format("[%s]%s", returnCode, returnMsg)).setCode(returnCode);
    }

    /**
     * 当前只配置了一种"兜底文案",但是保留了拓展能力
     * @param platform
     * @return
     */
    private SceneConfigRecord getFirstNonNullSceneConfigRecord(String platform) {
        List<String> fallbackCopies = Arrays.asList(FALLBACK_COPY);
        return getFirstNonNullSceneConfigRecordRecursive(fallbackCopies, platform, 0).join();
    }

    /**
     * 选择第一个返回不为null的兜底文案
     * @param fallbackCopies 兜底文案的关键字集合
     * @param platform 对接的第三方
     * @param index 兜底文案的关键字集合中的第几个元素
     * @return
     */
    private CompletableFuture<SceneConfigRecord> getFirstNonNullSceneConfigRecordRecursive(List<String> fallbackCopies, String platform, int index) {
        if (index >= fallbackCopies.size()) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> getSceneConfigRecord(fallbackCopies.get(index), platform))
                .thenCompose(result -> {
                    if (result != null) {
                        return CompletableFuture.completedFuture(result);
                    } else {
                        return getFirstNonNullSceneConfigRecordRecursive(fallbackCopies, platform, index + 1);
                    }
                });
    }

    private String getReturnMessage(String viewEndpoint, SceneConfigRecord sceneConfigRecord,String originalMsg) {
        String returnMessage = originalMsg;
        if (StrUtil.startWith(viewEndpoint, SPA, Boolean.TRUE)) {
            returnMessage = sceneConfigRecord.getSpaTipsZh();
        } else if (StrUtil.startWith(viewEndpoint, CRM, Boolean.TRUE)) {
            returnMessage = sceneConfigRecord.getCrmTipsZh();
        } else if (StrUtil.startWith(viewEndpoint, APP, Boolean.TRUE)) {
            returnMessage = sceneConfigRecord.getToBTipsZh();
        } else if (StrUtil.startWith(viewEndpoint, CONSUMER, Boolean.TRUE)) {
            returnMessage = sceneConfigRecord.getToCTipsZh();
        }
        return returnMessage;
    }

    /**
     * 获取错误码管理平台信息
     *
     * @param msg      关键词
     * @param platform 报错来源
     * @return
     */
    public SceneConfigRecord getSceneConfigRecord(String msg, String platform) {
        //调用错误码管理平台
        Map<String, Object> extendMap = new HashMap<>();
        extendMap.put(SOURCE_MEMO, platform);
        return SceneConfigFacade.getSceneConfigRecord("", msg, extendMap);
    }
}
