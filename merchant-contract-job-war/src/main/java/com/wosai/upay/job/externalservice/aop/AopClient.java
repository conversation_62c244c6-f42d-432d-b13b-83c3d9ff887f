package com.wosai.upay.job.externalservice.aop;

import com.alibaba.fastjson.JSON;
import com.wosai.aop.gateway.model.MerchantUserNoticeSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.app.dto.MerchantUserSimpleInfo;
import com.wosai.app.service.MerchantUserService;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.core.service.MerchantService;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * AOP通知客户端，用于发送各种商户通知
 * 优化版本：支持重试、批量发送、监控统计、配置化管理
 *
 * <AUTHOR>
 * @date 2025/8/26
 */
@Slf4j
@Component
public class AopClient {

    // 配置常量
    private static final String KEEP_ALIVE_NOTICE_PREFIX = "keep_alive_notice_sent:";

    @Autowired
    private ClientSideNoticeService clientSideNoticeService;
    @Autowired
    private MerchantUserService merchantUserService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private StringRedisTemplate redisTemplate;
    // 注入自定义线程池
    @Resource(name = "aopThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor aopThreadPoolTaskExecutor;


    /**
     * 发送启用保活配置通知（异步）
     *
     * @param merchantSn 商户序列号
     * @return 发送结果的Future
     */
    public CompletableFuture<NoticeResult> sendEnableKeepAliveConfig(String merchantSn) {
        return CompletableFuture.supplyAsync(() -> sendNotice(merchantSn, NoticeType.ENABLE_KEEP_ALIVE), aopThreadPoolTaskExecutor);
    }

    /**
     * 发送禁用保活配置通知（异步）
     *
     * @param merchantSn 商户序列号
     * @return 发送结果的Future
     */
    public CompletableFuture<NoticeResult> sendDisableKeepAliveConfig(String merchantSn) {
        return CompletableFuture.supplyAsync(() -> sendNotice(merchantSn, NoticeType.DISABLE_KEEP_ALIVE), aopThreadPoolTaskExecutor);
    }

    /**
     * 发送开始保活任务通知（带去重，异步）
     *
     * @param merchantSn 商户序列号
     * @return 发送结果的Future
     */
    public CompletableFuture<NoticeResult> sendStartKeepAliveNotice(String merchantSn) {
        return CompletableFuture.supplyAsync(() -> sendNoticeWithDeduplication(merchantSn, NoticeType.START_KEEP_ALIVE), aopThreadPoolTaskExecutor);
    }

    /**
     * 发送保活任务成功通知（异步）
     *
     * @param merchantSn 商户序列号
     * @return 发送结果的Future
     */
    public CompletableFuture<NoticeResult> sendKeepAliveTaskSuccessNotice(String merchantSn) {
        return CompletableFuture.supplyAsync(() -> sendNotice(merchantSn, NoticeType.SUCCESS_KEEP_ALIVE), aopThreadPoolTaskExecutor);
    }

    public CompletableFuture<NoticeResult> sendTonglianV2LegalSign(String merchantSn, Map data) {
        return CompletableFuture.supplyAsync(() -> sendNoticeWithData(merchantSn, NoticeType.TONGLIAN_V2_LEGAL_SIGN, data), aopThreadPoolTaskExecutor);
    }

    public CompletableFuture<NoticeResult> sendTonglianV2SettlementSign(String merchantSn, Map data) {
        return CompletableFuture.supplyAsync(() -> sendNoticeWithData(merchantSn, NoticeType.TONGLIAN_V2_SETTLEMENT_SIGN, data), aopThreadPoolTaskExecutor);
    }

    /**
     * 带去重的通知发送
     */
    private NoticeResult sendNoticeWithDeduplication(String merchantSn, NoticeType noticeType) {
        String redisKey = KEEP_ALIVE_NOTICE_PREFIX + merchantSn + ":" + noticeType.name();

        if (Boolean.TRUE.equals(redisTemplate.hasKey(redisKey))) {
            String message = String.format("商户 %s 的 %s 通知已在今天发送过，跳过发送", merchantSn, noticeType.getDescription());
            log.info(message);
            return new NoticeResult(true, message, merchantSn, noticeType);
        }

        NoticeResult result = sendNotice(merchantSn, noticeType);

        if (result.isSuccess()) {
            // 发送成功后，将记录存入Redis，有效期到当天24点
            long ttl = calculateTTLToMidnight();
            redisTemplate.opsForValue().set(redisKey, "1", ttl, TimeUnit.SECONDS);
        }

        return result;
    }

    private NoticeResult sendNotice(String merchantSn, NoticeType noticeType) {
        return sendNoticeWithData(merchantSn, noticeType, null);
    }

    /**
     * 核心通知发送方法（使用@Retryable注解支持重试）
     */
    private NoticeResult sendNoticeWithData(String merchantSn, NoticeType noticeType, Map data) {
        if (merchantSn == null || merchantSn.trim().isEmpty()) {
            return new NoticeResult(false, "商户号为空", merchantSn, noticeType);
        }

        // 1. 获取商户信息
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (merchant == null) {
            String message = "商户信息不存在";
            log.warn("{}, 商户SN: {}", message, merchantSn);
            return new NoticeResult(false, message, merchantSn, noticeType);
        }

        // 2. 获取商户用户信息
        MerchantUserSimpleInfo merchantUserInfo = merchantUserService.getSuperAdminSimpleInfoByMerchantId(
                WosaiMapUtils.getString(merchant, DaoConstants.ID));
        if (merchantUserInfo == null) {
            String message = "商户用户信息不存在";
            log.warn("{}, 商户SN: {}", message, merchantSn);
            return new NoticeResult(false, message, merchantSn, noticeType);
        }

        // 3. 构建并发送通知
        MerchantUserNoticeSendModel sendModel = buildNoticeModel(
                merchantUserInfo.getMerchant_user_id(), noticeType, data);

        Boolean sendResult = clientSideNoticeService.sendToMerchantUser(sendModel);

        if (Boolean.TRUE.equals(sendResult)) {
            log.info("{}发送通知成功，商户: {}, 内容: {}",
                    noticeType.getDescription(), merchantSn, JSON.toJSONString(sendModel));
            return new NoticeResult(true, "发送成功", merchantSn, noticeType);
        } else {
            String message = "通知服务返回失败";
            log.warn("{}发送通知失败，商户: {}, 原因: {}",
                    noticeType.getDescription(), merchantSn, message);
            return new NoticeResult(false, message, merchantSn, noticeType);
        }
    }

    /**
     * 构建通知模型
     *
     * @param merchantUserId 商户用户ID
     * @param noticeType     通知类型
     * @return 通知模型
     */
    private MerchantUserNoticeSendModel buildNoticeModel(String merchantUserId, NoticeType noticeType, Map data) {
        MerchantUserNoticeSendModel sendModel = new MerchantUserNoticeSendModel();
        sendModel.setDevCode(noticeType.getDevCode());
        sendModel.setTemplateCode(noticeType.getTemplateCode());
        sendModel.setMerchantUserId(merchantUserId);
        sendModel.setTimestamp(System.currentTimeMillis());
        List<String> clientSides = noticeType.getClientSides();
        if (WosaiCollectionUtils.isNotEmpty(clientSides)) {
            sendModel.setClientSides(clientSides);
        }
        if (WosaiMapUtils.isNotEmpty(data)) {
            sendModel.setData(data);
        }
        return sendModel;
    }

    /**
     * 计算到当天24点的剩余秒数
     */
    private long calculateTTLToMidnight() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime midnight = now.toLocalDate().plusDays(1).atStartOfDay();
        long secondsToMidnight = ChronoUnit.SECONDS.between(now, midnight);
        return Math.max(secondsToMidnight, 1);
    }

    /**
     * 通知类型枚举
     */
    @Getter
    public enum NoticeType {
        ENABLE_KEEP_ALIVE("TD8Z7KZQZTGP", "I0UDXL7CVVCT", "开启保活配置", Collections.EMPTY_LIST),
        DISABLE_KEEP_ALIVE("WI5ZKEWV3DLE", "I0UDXL7CVVCT", "关闭保活配置", Collections.EMPTY_LIST),
        START_KEEP_ALIVE("HLUARAF2DNDA", "I0UDXL7CVVCT", "准备执行保活任务", Collections.EMPTY_LIST),
        SUCCESS_KEEP_ALIVE("AN75KJ6ULQPS", "I0UDXL7CVVCT", "执行保活任务成功", Collections.EMPTY_LIST),
        TONGLIAN_V2_LEGAL_SIGN("IBHDXOXKYMTH", "I0UDXL7CVVCT", "通联收银宝法人签约", Arrays.asList("TERMINALSSQB", "TERMINALAPP")),
        TONGLIAN_V2_SETTLEMENT_SIGN("2G0KTZM6KQFV", "I0UDXL7CVVCT", "通联收银宝结算人签约", Arrays.asList("TERMINALSSQB", "TERMINALAPP")),
        ;

        private final String templateCode;
        private final String devCode;
        private final String description;
        private final List<String> clientSides;

        NoticeType(String templateCode, String devCode, String description, List<String> clientSides) {
            this.templateCode = templateCode;
            this.devCode = devCode;
            this.description = description;
            this.clientSides = clientSides;
        }
    }

    /**
     * 通知发送结果
     */
    @Data
    public static class NoticeResult {
        private final boolean success;
        private final String message;
        private final String merchantSn;
        private final NoticeType noticeType;
        private final LocalDateTime timestamp;

        public NoticeResult(boolean success, String message, String merchantSn, NoticeType noticeType) {
            this.success = success;
            this.message = message;
            this.merchantSn = merchantSn;
            this.noticeType = noticeType;
            this.timestamp = LocalDateTime.now();
        }

        @Override
        public String toString() {
            return String.format("NoticeResult{success=%s, merchantSn='%s', noticeType=%s, message='%s', timestamp=%s}",
                    success, merchantSn, noticeType, message, timestamp);
        }
    }
}
