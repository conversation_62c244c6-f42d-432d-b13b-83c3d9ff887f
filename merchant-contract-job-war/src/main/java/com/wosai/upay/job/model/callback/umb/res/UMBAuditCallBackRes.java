package com.wosai.upay.job.model.callback.umb.res;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class UMBAuditCallBackRes extends UMBCallBackBaseRes {
    /**
     * 信息复核状态状态 01：复核通过02：复核拒绝05：部分通过
     * 如果是CMS013返回7(即需要等待回调)，审核后通知状态只会是5或8，审核通过就是5，不通过就是8
     */
    private String checkstatus;
    /**
     * 原流水号
     */
    private String oritranflow;
    /**
     * 复核备注,复核拒绝时必输
     */
    private String remark;

    /**
     * 0：正常 5：未签约 8：认证审核失败
     */
    private String status;

    public boolean isAuditPass() {
        return "5".equals(status) || "05".equals(checkstatus) || "01".equals(checkstatus);
    }
}
