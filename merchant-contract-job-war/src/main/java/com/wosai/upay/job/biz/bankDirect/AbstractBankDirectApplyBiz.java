package com.wosai.upay.job.biz.bankDirect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.BankDirectApplyStatusEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.CommonEventHandler;
import com.wosai.upay.job.handlers.QueryContractStatusHandler;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.direct.ApplyStatusResp;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import com.wosai.upay.job.model.psbc.ModifySupportingMaterialsRequest;
import com.wosai.upay.job.model.psbc.SelfAuditRejectRequest;
import com.wosai.upay.job.service.ContractStatusService;
import com.wosai.upay.job.service.TaskResultService;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.util.WechatQrCodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.StringUtils;

import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.job.providers.PabProvider.PAB_SIGN_RULE;
import static com.wosai.upay.job.providers.PsbcProvider.replaceHttp;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/4/26 09:15
 */
@Slf4j
public abstract class AbstractBankDirectApplyBiz implements BankHandleService {

    @Autowired
    BankDirectApplyMapper bankDirectApplyMapper;


    @Autowired
    ContractTaskMapper contractTaskMapper;

    @Autowired
    TaskResultService taskResultService;

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    public ParamContextBiz paramContextBiz;

    @Autowired
    protected CommonEventHandler commonEventHandler;

    @Autowired
    protected RedisLock redisLock;

    @Autowired
    @Lazy
    private ContractStatusService contractStatusService;


    @Autowired
    protected DirectStatusBiz directStatusBiz;

    @Autowired
    MerchantService merchantService;

    @Autowired
    WechatQrCodeUtils wechatQrCodeUtils;
    @Autowired
    MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    RuleContext ruleContext;

    @Autowired
    QueryContractStatusHandler queryContractStatusHandler;

    @Autowired
    BusinessLogBiz businessLogBiz;

    @Autowired
    protected ApplicationApolloConfig applicationApolloConfig;

    @Value("${pab_dev_code}")
    public String pabDevCode;

    @Value("${hxb_dev_code}")
    public String hxbDevCode;

    /**
     * @param: BankDirectReq 申请参数
     * @return:
     * @Author: zhmh
     * @Description:
     * @time: 09:51 2021/4/7
     */
    @Override
    public ContractResponse applyBankDirect(@Valid BankDirectReq bankDirectReq) {
        log.info("AbstractBankDirectApplyBiz.applyBankDirect, bankDirectReq:{}",
            JSONObject.toJSONString(bankDirectReq));
        ContractResponse contractResponse = new ContractResponse();
        String key = String.format("applyBankDirect:%s_%s", bankDirectReq.getMerchant_sn(), bankDirectReq.getDev_code());
        String value = UUID.randomUUID().toString();
        // 加锁，防止重复提交
        if (!redisLock.lock(key, value, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS)) {
            return contractResponse.setMsg("网络不好,请稍后重试");
        }

        final String merchantSn = bankDirectReq.getMerchant_sn();
        final String devCode = bankDirectReq.getDev_code();

        try {
            final BankDirectApply directApply = bankDirectApplyMapper.getApplyBySnAndDevCode(merchantSn, devCode);
            // 如果有华夏终止的任务，重新唤起旧任务
//            if (hxbDevCode.equals(devCode)){
//                ContractTask contractTask = contractTaskMapper.getHXFailTask(merchantSn);
//                if (contractTask != null){
//                    contractSubTaskMapper.restartHxSubTask(contractTask.getId());
//                    contractTaskMapper.restartHXTask(contractTask.getId());
//                    return contractResponse.setSuccess(true).setMsg("业务开通成功");
//                }
//            }
            // 1. 前置校验，检查商户申请记录
            preCheck(directApply, merchantSn);

            // 2. 查询商户的直连申请状态
            if (Objects.nonNull(directApply)) {
                // 如果之前的申请失败且流程中有“合同成功”状态，则回退并重新插入
                if (isRecoverableFailure(directApply)) {
                    doInsert(directApply);
                    return contractResponse.setSuccess(true).setMsg("业务开通成功");
                }
            }

            // 3. 构建上下文信息并完善特定业务需要的信息
            final Map<String, Object> context = completeContext(
                    paramContextBiz.buildBankDirectBaseContext(bankDirectReq), bankDirectReq
            );

            // 4. 生成任务，并插入任务信息
            ContractEvent contractEvent = createContractEvent(merchantSn, context, devCode);
            commonEventHandler.handleInsert(contractEvent, context);

            // 获取任务ID，用于关联任务和申请记录
            final Long taskId = getTaskId(contractEvent.getRule_group_id(), merchantSn);

            // 5. 创建并插入直连申请记录
            final BankDirectApply bankDirectApply = populateBean(bankDirectReq, taskId);
            bankDirectApplyMapper.insertSelective(bankDirectApply);

            // 6. 记录操作日志
            businessLogBiz.recordBankLog(bankDirectApply);

            // 7. 更新流程状态并展示初始化流程
            updateViewProcess(bankDirectReq.getMerchant_sn(), bankDirectApply);

            // 8. 推送状态更新消息
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, devCode, DirectStatus.STATUS_PENDING, null);
        } catch (Exception e) {
            log.error("开通银行直连业务异常:{}", e);
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, devCode, DirectStatus.STATUS_BIZ_FAIL, null);
            return contractResponse.setSuccess(false).setMsg("开通失败: " + e.getMessage());
        } finally {
            redisLock.unlock(key, value);
        }

        return contractResponse.setSuccess(true).setMsg("业务开通成功");
    }

    /**
     * 判断是否是可以恢复的失败情况
     */
    protected boolean isRecoverableFailure(BankDirectApply directApply) {
        Map<String, Object> extraMap = directApply.getExtraMap();
        String processStatus = BeanUtil.getPropString(extraMap, BankDirectApplyConstant.Extra.PROCESS);
        List<Integer> processStatusList = Optional.ofNullable(JSONObject.parseArray(processStatus, ProcessStatus.class))
                .orElseGet(ArrayList::new)
                .stream()
                .map(ProcessStatus::getProcessStatus)
                .collect(Collectors.toList());

        return Objects.equals(directApply.getStatus(), BankDirectApplyConstant.Status.FAIL)
                && processStatusList.contains(BankDirectApplyConstant.ProcessStatus.CONTRACT_SUCCESS)
                && processStatusList.remove(BankDirectApplyConstant.ProcessStatus.FAIL);
    }

    /**
     * 创建商户任务事件
     */
    protected ContractEvent createContractEvent(String merchantSn, Map<String, Object> context, String devCode) {
        ContractEvent contractEvent = new ContractEvent();
        contractEvent.setMerchant_sn(merchantSn);
        String groupId = chooseGroupByContextAndDevCode(context, devCode);
        contractEvent.setRule_group_id(groupId);
        contractEvent.setEvent_type(ContractEvent.OPT_TYPE_NET_IN);
        return contractEvent;
    }

    /**
     * 更新流程展示和状态
     */
    protected void updateViewProcess(String merchantSn, BankDirectApply bankDirectApply) {
        List<ViewProcess> viewProcessList = initViewProcess(merchantSn);
        if (CollectionUtils.isNotEmpty(viewProcessList)) {
            // 获取最新数据，防止覆盖extra字段
            BankDirectApply apply = bankDirectApplyMapper.selectByPrimaryKey(bankDirectApply.getId());
            Map<String, Object> extraMap = apply.getExtraMap();
            extraMap.put(BankDirectApplyConstant.Extra.VIEW_PROCESS, viewProcessList);

            BankDirectApply updatedApply = new BankDirectApply();
            updatedApply.setId(bankDirectApply.getId());
            updatedApply.setExtra(JSONObject.toJSONString(extraMap));
            bankDirectApplyMapper.updateByPrimaryKeySelective(updatedApply);
        }
    }


    protected void doInsert(BankDirectApply directApply) {
        //去除失败的记录
        final Map<String, Object> extraMap = directApply.getExtraMap();
        final String processStatus = BeanUtil.getPropString(extraMap, BankDirectApplyConstant.Extra.PROCESS);
        final List<ProcessStatus> processStatusList = JSONObject.parseArray(processStatus, ProcessStatus.class);
        //微信认证超时失败,重新提交需要更新task时间
        if (org.apache.commons.lang3.StringUtils.equals(directApply.getResult(), BankDirectApplyConstant.AUTH_TIME_OUT_MEMO)) {
            final Long taskId = directApply.getTask_id();
            contractTaskMapper.updatePriority(StringUtil.formatDate(System.currentTimeMillis()), taskId);
            log.info("开通银行业务,微信商家认证超时,重新修改task时间:{}", taskId);
        }
        processStatusList.sort(Comparator.comparing(ProcessStatus::getProcessStatus));
        final List<ProcessStatus> statuses = processStatusList.stream().limit(2).collect(Collectors.toList());
        extraMap.put(BankDirectApplyConstant.Extra.PROCESS, statuses);
        directApply.setId(null);
        directApply.setStatus(BankDirectApplyConstant.Status.APPLYING);
        directApply.setResult(null);
        directApply.setProcess_status(BankDirectApplyConstant.ProcessStatus.CONTRACT_SUCCESS);
        directApply.setCreate_at(new Date());
        directApply.setExtra(JSONObject.toJSONString(extraMap));
        directApply.setUpdate_at(new Date());
        directApply.setPriority(new Date());
        bankDirectApplyMapper.insertSelective(directApply);
    }

    @Override
    public ApplyStatusResp getApplyInfo(String merchantSn, String devCode, String platform) {
        final ApplyStatusResp resp = new ApplyStatusResp();
        final BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyBySnAndDevCode(merchantSn, devCode);
        if (Objects.isNull(bankDirectApply)) {
            return resp.setContract_memo(BankDirectApplyStatusEnum.NO_TASK.getMessage()).setContract_code(BankDirectApplyStatusEnum.NO_TASK.getCode()).setStatus(BankDirectApplyConstant.APPLYING);
        }
        final Integer status = bankDirectApply.getStatus();
        //申请单状态0-已提交,10-申请中,20-申请成功,30-申请失败
        if (Objects.equals(BankDirectApplyConstant.Status.PENDING, status)) {
            resp.setContract_memo(BankDirectApplyStatusEnum.PENDING_TASK.getMessage()).setContract_code(BankDirectApplyStatusEnum.PENDING_TASK.getCode()).setStatus(BankDirectApplyConstant.APPLYING);
        }
        if (Objects.equals(BankDirectApplyConstant.Status.APPLYING, status)) {
            resp.setContract_memo(BankDirectApplyStatusEnum.BANK_AUDITING.getMessage()).setContract_code(BankDirectApplyStatusEnum.BANK_AUDITING.getCode()).setStatus(BankDirectApplyConstant.APPLYING);
        }
        if (Objects.equals(BankDirectApplyConstant.Status.SUCCESS, status)) {
            resp.setContract_memo(BankDirectApplyStatusEnum.BANK_TASK_SUCCESS.getMessage()).setContract_code(BankDirectApplyStatusEnum.BANK_TASK_SUCCESS.getCode()).setStatus(BankDirectApplyConstant.SUCCESS);
        }
        if (Objects.equals(BankDirectApplyConstant.Status.FAIL, status)) {
            final Long taskId = bankDirectApply.getTask_id();
            final ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(taskId);
            if (Objects.isNull(contractTask)) {
                return resp.setContract_memo(BankDirectApplyStatusEnum.UNKNOWN_CODE.getMessage() + ": " + bankDirectApply.getResult())
                        .setContract_code(BankDirectApplyStatusEnum.UNKNOWN_CODE.getCode())
                        .setStatus(BankDirectApplyConstant.FAIL);
            }
            String message = null;
            if (Objects.equals(contractTask.getStatus(), TaskStatus.FAIL.getVal())) {
                //文案提示转义
                message = contractStatusService.getMessageByContractTaskV2(contractTask, platform);

            }
            //进件成功但是后续银行卡设置失败
            if (Objects.equals(contractTask.getStatus(), TaskStatus.SUCCESS.getVal())) {
                //文案提示转义
                message = bankDirectApply.getResult();
            }
            if (StringUtils.isEmpty(message)) {
                message = BankDirectApplyStatusEnum.UNKNOWN_CODE.getMessage();
            }
            resp.setContract_memo(message).setContract_code(BankDirectApplyStatusEnum.UNKNOWN_CODE.getCode()).setStatus(BankDirectApplyConstant.FAIL);
        }
        return resp;
    }

    @Override
    public Integer getApplyStatus(String merchantSn, String devCode) {
        final BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyBySnAndDevCode(merchantSn, devCode);
        if (Objects.isNull(bankDirectApply)) {
            throw new CommonPubBizException("没有找到对应申请");
        }
        final Integer status = bankDirectApply.getStatus();
        //申请单状态0-已提交,10-申请中,20-申请成功,30-申请失败
        if (Objects.equals(BankDirectApplyConstant.Status.PENDING, status) || Objects.equals(BankDirectApplyConstant.Status.APPLYING, status)) {
            return BankDirectApplyConstant.APPLYING;
        }
        if (Objects.equals(BankDirectApplyConstant.Status.SUCCESS, status)) {
            return BankDirectApplyConstant.SUCCESS;
        }
        if (Objects.equals(BankDirectApplyConstant.Status.FAIL, status)) {
            return BankDirectApplyConstant.FAIL;
        }
        return BankDirectApplyConstant.APPLYING;
    }

    @Override
    public Integer getContractTaskStatus(String merchantSn, String devCode) {
        final BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyBySnAndDevCode(merchantSn, devCode);
        if (Objects.isNull(bankDirectApply) || Objects.isNull(bankDirectApply.getTask_id())) {
            return null;
        }
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(bankDirectApply.getTask_id());
        return contractTask == null ? null : contractTask.getStatus();
    }


    /**
     * 获取申请单的状态
     *
     * @param merchantSn 商户号
     * @param devCode    应用标识
     * @return 状态
     */
    @Override
    public List<ViewProcess> getViewProcess(String merchantSn, String devCode) {
        final BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyBySnAndDevCode(merchantSn, devCode);
        if (Objects.isNull(bankDirectApply)) {
            throw new CommonPubBizException("没有找到对应申请");
        }
        final Integer status = bankDirectApply.getStatus();
        //申请单状态0-已提交,10-申请中,20-申请成功,30-申请失败
        if (!Objects.equals(BankDirectApplyConstant.Status.APPLYING, status)) {
            throw new CommonPubBizException("商户状态不是申请中");
        }
        final Map<String, Object> extraMap = bankDirectApply.getExtraMap();
        final String view = BeanUtil.getPropString(extraMap, BankDirectApplyConstant.Extra.VIEW_PROCESS);
        if (StringUtils.isEmpty(view)) {
            return Lists.newArrayList();
        }
        final List<ViewProcess> processes = JSONObject.parseArray(view, ViewProcess.class);
        final boolean match = processes.stream().anyMatch(process -> !StringUtils.isEmpty(process.getExtraMessage()) && !process.getExtraMessage().contains("wosai-images.oss-cn-hangzhou.aliyuncs.com"));
        if (match) {
            processes.stream().forEach(process -> {
                if (!StringUtils.isEmpty(process.getExtraMessage()) && !process.getExtraMessage().contains("wosai-images.oss-cn-hangzhou.aliyuncs.com")) {
                    final String newUrl = getWXImageUrl(getAcquire(), getProvider());
                    process.setExtraMessage(newUrl);
                }
            });
            extraMap.put(BankDirectApplyConstant.Extra.VIEW_PROCESS, processes);
            bankDirectApplyMapper.updateByPrimaryKeySelective(new BankDirectApply().setId(bankDirectApply.getId()).setExtra(JSONObject.toJSONString(extraMap)));
        }
        //还未完成的不展示时间
        processes.stream().forEach(x -> {
            if (!x.getFinish()) {
                x.setTime(null);
            }
        });
        postHandleViewProcess(bankDirectApply, processes);
        return processes;
    }

    protected void postHandleViewProcess(BankDirectApply bankDirectApply, List<ViewProcess> processes) {
    }


    /**
     * <AUTHOR>
     * @Description: 填充BankDirectApply 实例对象属性
     * @time 10:16
     */
    public BankDirectApply populateBean(BankDirectReq bankDirectReq, Long taskId) {
        final BankDirectApply bankDirectApply = new BankDirectApply();
        bankDirectApply.setMerchant_sn(bankDirectReq.getMerchant_sn());
        bankDirectApply.setTask_id(taskId);
        bankDirectApply.setDev_code(bankDirectReq.getDev_code());
        Integer bank_ref = getBankRef(bankDirectReq.getDev_code());
        bankDirectApply.setBank_ref(bank_ref);
        bankDirectApply.setStatus(BankDirectApplyConstant.Status.PENDING);
        bankDirectApply.setResult("已提交请等待");
        bankDirectApply.setForm_body(bankDirectReq.getForm_body());
        final Map map = CollectionUtil.hashMap(BankDirectApplyConstant.Extra.ACQUIRE, getAcquire(), BankDirectApplyConstant.Extra.PROVIDER, getProvider());
        //建行批量导入
        String formBody = bankDirectReq.getForm_body();
        Map context = JSONObject.parseObject(formBody, Map.class);
        String from = BeanUtil.getPropString(context, "from");
        if (Objects.equals(from, "batch_import")) {
            map.put(BankDirectApplyConstant.Extra.DATA_FROM, "batch_import");
        }
        bankDirectApply.setExtra(JSONObject.toJSONString(map));
        return bankDirectApply;

    }

    /**
     * @param groupId    规则组Id
     * @param merchantSn 商户号
     * @return null
     * <AUTHOR>
     * @Description:
     * @time 09:18
     */
    public Long getTaskId(String groupId, String merchantSn) {
        final List<ContractTask> contractTaskList = contractTaskMapper.getContractsBySnAndType(merchantSn, "新增商户入网");
        final List<ContractTask> contractTasks = Optional.ofNullable(contractTaskList)
                .orElseGet(ArrayList::new)
                .stream()
                .filter(x -> Objects.equals(groupId, x.getRule_group_id()) && Lists.newArrayList(TaskStatus.PROGRESSING.getVal(), TaskStatus.PENDING.getVal()).contains(x.getStatus()))
                .sorted(Comparator.comparing(ContractTask::getCreate_at, Comparator.nullsLast(Date::compareTo)).reversed())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(contractTasks)) {
            throw new CommonPubBizException(String.format("商户号%s还没有生成进件任务", merchantSn));
        }
        return contractTasks.get(0).getId();
    }


    /**
     * @param: merchantSn 商户号
     * @param: devCode code
     * @Author: zhmh
     * @Description: 检验商户时候报备完成
     * @time: 11:06 2021/4/7
     */
    public void preCheck(BankDirectApply bankDirectApply, String merchantSn) {
        final int status = contractStatusService.getContractStatus(merchantSn);
        if (!Objects.equals(ContractStatus.STATUS_SUCCESS, status)) {
            throw new CommonInvalidParameterException("商户间连扫码还未开通");
        }
        if (Objects.nonNull(bankDirectApply) && Objects.equals(bankDirectApply.getStatus(), BankDirectApplyConstant.Status.SUCCESS)) {
            throw new CommonInvalidParameterException("业务已开通,请勿重新提交");
        }
        if (Objects.nonNull(bankDirectApply) && !Objects.equals(bankDirectApply.getStatus(), BankDirectApplyConstant.Status.FAIL)) {
            throw new CommonInvalidParameterException("业务开通中,请耐心等待");
        }
    }

    protected abstract String chooseGroupByContextAndDevCode(Map<String, Object> context, String devCode);

    protected abstract Map<String, Object> completeContext(Map<String, Object> paramContext, BankDirectReq bankDirectReq);

    protected abstract Integer getBankRef(String dev_code);

    protected abstract String getAcquire();

    protected abstract Integer getProvider();


    @Override
    public ContractResponse selfAuditReject(SelfAuditRejectRequest request) {
        String merchantId = request.getMerchantId();
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        //前置校验
        ContractSubTask contractSubTask = selfAuditRejectPreCheck(merchant, getDevCode());
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);

        //银行收单机构撤回处理
        //注:子类实现不要直接抛异常，返回ContractResponse的code为不成功即可
        com.wosai.upay.merchant.contract.model.ContractResponse merInfoAddRevocation = doReject(merchantSn, request, contractSubTask);

        //撤回结果处理
        if (!merInfoAddRevocation.isSuccess()) {
            throw new CommonPubBizException(merInfoAddRevocation.getMessage());
        }
        //设置主任务和进件子任务失败
        setTaskAndSubTaskFail(contractSubTask);

        //银行收单机构后置处理
        rejectPostHandle(contractSubTask);

        //成功返回结果
        return rejectSuccess(merInfoAddRevocation.getMessage());
    }

    /**
     * 入网任务置为失败后 收单机构的处理
     */
    public void rejectPostHandle(ContractSubTask contractSubTask) {

    }

    /**
     * 撤回处理
     *
     * @param merchantSn
     * @param request
     * @return
     */
    public com.wosai.upay.merchant.contract.model.ContractResponse doReject(String merchantSn, SelfAuditRejectRequest request, ContractSubTask contractSubTask) {
        throw new CommonPubBizException("不支持自助驳回");
    }

    /**
     * 自助驳回前置校验
     *
     * @param merchant
     * @param devCode
     */
    public ContractSubTask selfAuditRejectPreCheck(Map merchant, String devCode) {
        if (MapUtils.isEmpty(merchant)) {
            throw new CommonPubBizException("商户不存在");
        }
        final String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        //当前是否有任务
        final BankDirectApply directApply = bankDirectApplyMapper.getApplyBySnAndDevCode(merchantSn, devCode);
        if (directApply == null) {
            throw new CommonPubBizException("还未生成进件任务");
        }
        final Long taskId = directApply.getTask_id();
        final List<ContractSubTask> acquireSubTask = contractSubTaskMapper.getAcquireSubTask(taskId);
        if (org.springframework.util.CollectionUtils.isEmpty(acquireSubTask)) {
            throw new CommonPubBizException("还未生成进件任务");
        }
        //创建时间倒序
        acquireSubTask.sort(Comparator.comparing(ContractSubTask::getCreate_at).reversed());
        //平安银行业务入网且商户签约成功后不允许自助驳回,所以需要获取签约任务
        ContractSubTask contractSubTask;
        if (pabDevCode.equals(getDevCode())) {
            contractSubTask = acquireSubTask.stream()
                    .filter(sub -> Objects.equals(sub.getContract_rule(), PAB_SIGN_RULE))
                    .findFirst()
                    .orElseGet(() -> acquireSubTask.get(0));
        } else {
            contractSubTask = acquireSubTask.get(0);
        }

        //进件失败
        if (Objects.equals(contractSubTask.getStatus(), TaskStatus.FAIL.getVal())) {
            throw new CommonPubBizException("银行进件任务失败,不需要撤销");
        }
        //进件成功不允许撤销
        if (Objects.equals(contractSubTask.getStatus(), TaskStatus.SUCCESS.getVal())) {
            throw new CommonPubBizException("银行审核成功,不允许撤销");
        }
        //还未开始进件
        if (StringUtils.isEmpty(contractSubTask.getContract_id())) {
            throw new CommonPubBizException("银行进件还未开始,不允许撤销");
        }
        return contractSubTask;
    }

    /**
     * 设置主任务和进件子任务失败
     *
     * @param contractSubTask
     */
    public void setTaskAndSubTaskFail(ContractSubTask contractSubTask) {
        //终止task任务,BankDirectApply任务查询到task任务终止会自动终止的
        String result = JSON.toJSONString(MapUtil.hashMap("message", "自助驳回成功", "result", "自助驳回成功"));
        contractSubTask.setStatus(6).setResult(result);
        contractSubTaskMapper.updateByPrimaryKey(contractSubTask);
        taskResultService.changeStatusAndResultV2(contractSubTask.getP_task_id(), contractSubTask.getId(), TaskStatus.FAIL.getVal(), result, false);
    }

    /**
     * 驳回成功返回
     *
     * @param message
     * @return
     */
    public ContractResponse rejectSuccess(String message) {
        final ContractResponse response = new ContractResponse();
        response.setMsg(StringUtils.isEmpty(message) ? "自助驳回成功" : message);
        response.setSuccess(Boolean.TRUE);
        return response;
    }


    /**
     * 初始化申请单的状态
     *
     * @param merchantSn 商户号
     * @return 状态
     */
    @Override
    public List<ViewProcess> initViewProcess(String merchantSn) {
        return Lists.newArrayList();
    }

    /**
     * 初始化信息
     *
     * @param acquire
     * @return
     */
    public List<ViewProcess> preViewProcess(String acquire) {
        final List<Map<String, String>> process = applicationApolloConfig.getBankViewProcess().get(acquire);
        if (org.springframework.util.CollectionUtils.isEmpty(process)) {
            return null;
        }
        return JSONObject.parseArray(JSONObject.toJSONString(process), ViewProcess.class);
    }

    /**
     * 获取微信图片地址
     */
    public String getWXImageUrl(String acquire, Integer provider) {
        String merchantChannelCode;
        String imageUrl = null;
        try {
            merchantChannelCode = wechatQrCodeUtils.authorizationCodeUrl(Maps.newHashMap(), String.format("%s-%s-3", acquire, provider));
            imageUrl = replaceHttp(merchantChannelCode);
        } catch (IOException e) {
            log.error("初始化审核流程:{},获取微信授权图片地址异常:{}", String.format("%s-%s-3", acquire, provider), e);
        }
        return imageUrl;
    }

    @Override
    public ContractResponse modifySupportingMaterials(ModifySupportingMaterialsRequest req) {
        return new ContractResponse().setSuccess(Boolean.FALSE).setMsg("不支持修改");
    }

    @Override
    public void refreshAndHandleContractStatus(String merchantSn, String devCode) {
        String key = String.format("refreshAndHandleContractStatus:%s_%s", merchantSn, devCode);
        String value = UUID.randomUUID().toString();
        if (!redisLock.lock(key, value, ProviderUtil.REDIS_KEY_EXPIRE_TEN_MINUTES)) {
            return;
        }
        try {
            final BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyBySnAndDevCode(merchantSn, devCode);
            if (Objects.isNull(bankDirectApply)) {
                throw new CommonPubBizException("没有找到对应申请");
            }
            final Integer status = bankDirectApply.getStatus();
            //申请单状态0-已提交,10-申请中,20-申请成功,30-申请失败
            if (!Objects.equals(BankDirectApplyConstant.Status.APPLYING, status)) {
                throw new CommonPubBizException("商户状态不是申请中");
            }
            ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(bankDirectApply.getTask_id());
            List<ContractSubTask> contractSubTaskList = contractSubTaskMapper.selectByPTaskId(contractTask.getId());
            for (ContractSubTask contractSubTask : contractSubTaskList) {
                if (contractSubTask.getStatus() == 1) {
                    queryContractStatusHandler.doHandle(contractTask, contractSubTask);
                }
            }
        } finally {
            redisLock.unlock(key, value);
        }
    }

    /**
     * <AUTHOR>
     * @Description: 使用extra中view_process字段记录状态变化供crm显示
     * @time 09:15
     */
    public void recordViewProcess(BankDirectApply apply, Integer viewStatus, Date date) {
        if (Objects.isNull(apply)) {
            return;
        }
        Map<String, Object> extraMap = apply.getExtraMap();
        if (MapUtils.isEmpty(extraMap)) {
            return;
        }
        //原有记录
        final String processStr = BeanUtil.getPropString(extraMap, BankDirectApplyConstant.Extra.VIEW_PROCESS);
        if (StringUtils.isEmpty(processStr)) {
            final List<ViewProcess> viewProcesses = initViewProcess(apply.getMerchant_sn());
            if (org.springframework.util.CollectionUtils.isEmpty(viewProcesses)) {
                return;
            }
            final Map<Integer, ViewProcess> processMap = viewProcesses.stream().collect(Collectors.toMap(x -> x.getViewStatus(), x -> x, (key1, key2) -> key1));
            //传过来的状态不存在
            if (!processMap.containsKey(viewStatus)) {
                return;
            }
            //初始化 这一步也是为了兼容之前没有的数据这样就不需要清洗数据了
            viewProcesses.forEach(process -> {
                if (process.getViewStatus() <= viewStatus) {
                    process.setFinish(Boolean.TRUE);
                    final String timeTemplate = process.getTime();
                    final String time = timeTemplate.replace("#", StringUtil.formatDate("yyyy-MM-dd HH:mm:ss", date));
                    process.setTime(time);
                }
            });
            extraMap.put(BankDirectApplyConstant.Extra.VIEW_PROCESS, viewProcesses);
        } else {
            final List<ViewProcess> processes = JSONObject.parseArray(processStr, ViewProcess.class);
            //状态已经处于使用中就直接返回,避免长时间调用数据库
            final boolean match = processes.stream().anyMatch(x -> Objects.equals(x.getViewStatus(), viewStatus) && x.getFinish());
            if (match) {
                return;
            }
            processes.forEach(process -> {
                if (process.getViewStatus() <= viewStatus && !process.getFinish()) {
                    process.setFinish(Boolean.TRUE);
                    final String timeTemplate = process.getTime();
                    final String time = timeTemplate.replace("#", StringUtil.formatDate("yyyy-MM-dd HH:mm:ss", date));
                    process.setTime(time);
                }
            });
            extraMap.put(BankDirectApplyConstant.Extra.VIEW_PROCESS, processes);
        }
        apply.setExtra(JSONObject.toJSONString(extraMap));
        bankDirectApplyMapper.updateByPrimaryKeySelective(apply);
    }

    public void replaceApplyViewProcessRemark(BankDirectApply apply, int viewStatus, String message) {
        if (Objects.isNull(apply) || WosaiStringUtils.isEmpty(message)) {
            return;
        }
        Map<String, Object> extraMap = apply.getExtraMap();
        if (WosaiMapUtils.isEmpty(extraMap)) {
            return;
        }
        //原有记录
        final String processStr = BeanUtil.getPropString(extraMap, BankDirectApplyConstant.Extra.VIEW_PROCESS);
        if (WosaiStringUtils.isEmpty(processStr)) {
            return;
        }
        final List<ViewProcess> processes = JSONObject.parseArray(processStr, ViewProcess.class);
        for (ViewProcess process : processes) {
            if (process.getViewStatus() == viewStatus) {
                if (message.equals(process.getRemark())) {
                    return;
                } else {
                    process.setRemark(message);
                }
            }
        }
        extraMap.put(BankDirectApplyConstant.Extra.VIEW_PROCESS, processes);
        BankDirectApply update = new BankDirectApply()
                .setId(apply.getId())
                .setExtra(JSON.toJSONString(extraMap));
        apply.setExtra(JSON.toJSONString(extraMap));
        bankDirectApplyMapper.updateByPrimaryKeySelective(update);
    }
}
