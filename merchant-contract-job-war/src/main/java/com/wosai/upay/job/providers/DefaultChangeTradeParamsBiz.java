package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.market.boss.circle.user.api.dto.IFeeds;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.sales.merchant.business.model.AppInfoModel;
import com.wosai.sales.merchant.business.service.common.CommonAppInfoService;
import com.wosai.service.PaywayActivityService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.helper.MyObjectMapper;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.adapter.apollo.BatchTemplateApolloConfig;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.comboparams.SubAppIdSyns;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.exception.ChangeParamsIgnoreException;
import com.wosai.upay.job.externalservice.brand.BrandBusinessClient;
import com.wosai.upay.job.externalservice.brand.model.BrandMerchantInfoQueryResp;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.PayParamsModel;
import com.wosai.upay.job.model.acquirer.MerchantTradeConfig;
import com.wosai.upay.job.model.subBizParams.SubBizConfig;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.McContractRuleDAO;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.event.TradeParamsChangeEvent;
import com.wosai.upay.job.refactor.model.entity.McContractRuleDO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.service.ConfigSupportService;
import com.wosai.upay.job.service.FoodCardServiceImpl;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.util.ThreadLocalUtil;
import com.wosai.upay.merchant.contract.model.MerchantProviderTradeParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.job.model.PayParamsModel.*;

/**
 * 切换交易参数处理类
 *
 * <AUTHOR>
 * @date 2021-04-09
 */
@Component
@Slf4j
public class DefaultChangeTradeParamsBiz {

    private static String SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY = "provider_trade_params_key";

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private ConfigSupportService configSupportService;
    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private RuleContext ruleContext;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private AgentAppidBiz agentAppidBiz;
    @Autowired
    SubAppIdSyns subAppIdSyns;

    @Autowired
    ComposeAcquirerBiz acquirerBiz;

    @Autowired
    private SupportService supportService;

    @Autowired
    private WxSettlementIdChangeBiz wxSettlementIdChangeBiz;

    @Autowired
    SubBizParamsBiz subBizParamsBiz;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    private BusinessLogBiz businessLogBiz;

    @Autowired
    CommonAppInfoService commonAppInfoService;
    @Autowired
    BackAcquirerBiz backAcquirerBiz;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    MerchantProviderParamsBiz merchantProviderParamsBiz;
    @Autowired
    private PaywayActivityService paywayActivityService;

    @Autowired
    private McProviderDAO mcProviderDAO;

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;
    @Autowired
    private BatchTemplateApolloConfig batchTemplateApolloConfig;

    @Autowired
    @Lazy
    private MerchantProviderParamsService merchantProviderParamsService;

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private JdBiz jdBiz;

    @Autowired
    private McContractRuleDAO mcContractRuleDAO;
    @Autowired
    private BrandBusinessClient brandBusinessClient;
    @Autowired
    BusinssCommonService businssCommonService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;



    @Value("${payment}")
    private String payment;


    @Value("${bank_cooperation}")
    private String bankCooperation;

    /**
     * 支付业务、银行合作以及校园饭卡数据需要写在merchant-config表
     */

    public List<String> modifyMerchantConfigAppId;
    

    @PostConstruct
    public void init() {
        modifyMerchantConfigAppId = Lists.newArrayList(payment, bankCooperation);
    }

    /**
     * subPayway 与受理商的映射 (merchant_config merchant_config_custom)
     */
    @SuppressWarnings("unchecked")
    private static final Map<String, String> subPaywayAgentNameColName = CollectionUtil.hashMap(
            SUB_PAYWAY_BARCODE + "", MerchantConfig.B2C_AGENT_NAME,
            SUB_PAYWAY_QRCODE + "", MerchantConfig.C2B_AGENT_NAME,
            SUB_PAYWAY_WAP + "", MerchantConfig.WAP_AGENT_NAME,
            SUB_PAYWAY_MINI + "", MerchantConfig.MINI_AGENT_NAME,
            SUB_PAYWAY_APP + "", MerchantConfig.APP_AGENT_NAME,
            SUB_PAYWAY_H5 + "", MerchantConfig.H5_AGENT_NAME
    );

    /**
     * subPayway 与 对应是否正式字段的映射
     */
    @SuppressWarnings("unchecked")
    private static final Map<String, String> subPaywayFormalColName = CollectionUtil.hashMap(
            SUB_PAYWAY_BARCODE + "", MerchantConfig.B2C_FORMAL,
            SUB_PAYWAY_QRCODE + "", MerchantConfig.C2B_FORMAL,
            SUB_PAYWAY_WAP + "", MerchantConfig.WAP_FORMAL,
            SUB_PAYWAY_MINI + "", MerchantConfig.MINI_FORMAL,
            SUB_PAYWAY_APP + "", MerchantConfig.APP_FORMAL,
            SUB_PAYWAY_H5 + "", MerchantConfig.H5_FORMAL

    );

    private Boolean judgeAcquirer(String merchantSn, String acquirer) {
        String merchantAcquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        if (AcquirerTypeEnum.LKL_V3.getValue().equalsIgnoreCase(merchantAcquirer)
                || AcquirerTypeEnum.LKL.getValue().equalsIgnoreCase(merchantAcquirer)) {
            return AcquirerTypeEnum.LKL_V3.getValue().equalsIgnoreCase(acquirer) || AcquirerTypeEnum.LKL.getValue().equalsIgnoreCase(acquirer);
        }
        return acquirer.equalsIgnoreCase(merchantAcquirer);
    }

    /**
     * 切换交易参数
     *
     * @param merchantProviderParams
     * @param fee
     * @param sync                   是否使用同步方式配置appid
     * @param tradeAppId
     * @return
     */
    public boolean changeTradeParams(MerchantProviderParams merchantProviderParams, String fee, boolean sync, String tradeAppId) {
        ContractChannel contractChannel = ruleContext.getContractChannel(merchantProviderParams.getPayway(), String.valueOf(merchantProviderParams.getProvider()), merchantProviderParams.getChannel_no());
        if (Objects.equals(subBizParamsBiz.getPayTradeAppId(), tradeAppId) && !judgeAcquirer(merchantProviderParams.getMerchant_sn(), contractChannel.getAcquirer())) {
            String msg = String.format("商户 %s 交易参数 %s 与商户收单机构不一致，禁止切换", merchantProviderParams.getMerchant_sn(), merchantProviderParams.getId());
            log.info(msg);
            throw new CommonPubBizException(msg);
        }
        // 如果是品牌商户、并且对应的payway在品牌模式，不允许切换，除非这次是强切
        Map merchant = merchantService.getMerchantBySn(merchantProviderParams.getMerchant_sn());
        BrandMerchantInfoQueryResp brandMerchantInfo = brandBusinessClient.getBrandMerchantInfoByMerchantId(WosaiMapUtils.getString(merchant, DaoConstants.ID));
        if (brandMerchantInfo.isBrandMerchant() && ThreadLocalUtil.getCheckBrandPaymentMode()) {
            if (brandMerchantInfo.getPaymentMode().equals(PaymentModeEnum.WX_BRAND_MODE) && merchantProviderParams.getPayway() == PAYWAY_WEIXIN) {
                throw new CommonPubBizException("品牌商户，微信品牌模式，不允许切换微信交易参数");
            }
            if (brandMerchantInfo.getPaymentMode().equals(PaymentModeEnum.ALI_BRAND_MODE) && merchantProviderParams.getPayway() == PAYWAY_ALIPAY) {
                throw new CommonPubBizException("品牌商户，支付宝品牌模式，不允许切换支付宝交易参数");
            }
            if (brandMerchantInfo.getPaymentMode().equals(PaymentModeEnum.ALI_WX_BRAND_MODE) && (merchantProviderParams.getPayway() == PAYWAY_ALIPAY || merchantProviderParams.getPayway() == PAYWAY_WEIXIN)) {
                throw new CommonPubBizException("品牌商户，支付宝/微信品牌模式，不允许切换支付宝/微信交易参数");
            }
        }
        // 检查是否在切换收单机构
        RLock lock = redissonClient.getLock("changeAcquirer:" + merchantProviderParams.getMerchant_sn());
        if (lock.isLocked() && !lock.isHeldByCurrentThread()) {
            throw new CommonPubBizException("商户正在切换收单机构，请稍后重试");
        }
        if (ProviderEnum.PROVIDER_HAIKE.getValue().equals(merchantProviderParams.getProvider())) {
            if (merchantProviderParams.getPayway().equals(PaywayEnum.UNIONPAY.getValue())) {
                if (!merchantProviderParamsBiz.checkHaikeUnionSuccess(merchantProviderParams)) {
                    String msg = String.format("商户 %s 交易参数 %s 与银联云闪付未报备成功，禁止切换", merchantProviderParams.getMerchant_sn(), merchantProviderParams.getId());
                    log.info(msg);
                    throw new ChangeParamsIgnoreException(msg);
                }
            }
            merchantProviderParamsBiz.syncHaikeTerminal(merchantProviderParams);
        }

        changeTradeParamsWithoutCheckAcquirer(merchantProviderParams, fee, sync, tradeAppId);

        //merchant_provider_params表只会记录移动支付业务的交易参数的使用状态
        if (!Objects.equals(subBizParamsBiz.getPayTradeAppId(), tradeAppId)) {
            return Boolean.TRUE;
        }
        MerchantProviderParamsExample merchantProviderParamsExample = new MerchantProviderParamsExample();
        merchantProviderParamsExample.or()
                .andMerchant_snEqualTo(merchantProviderParams.getMerchant_sn())
                .andPaywayEqualTo(merchantProviderParams.getPayway())
                .andIdNotEqualTo(merchantProviderParams.getId())
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andProviderNotEqualTo(merchantProviderParams.getPayway())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> upList = merchantProviderParamsMapper.selectByExample(merchantProviderParamsExample);
        //设置当前子商户号为已使用
        upList.forEach(u -> {
            merchantProviderParamsMapper.updateStatusByKey(UseStatusEnum.NO_USE.getValue(), System.currentTimeMillis(), u.getId());
        });
        merchantProviderParamsMapper.updateStatusByKey(UseStatusEnum.IN_USE.getValue(), System.currentTimeMillis(), merchantProviderParams.getId());
        if (Objects.equals(merchantProviderParams.getPayway(), PaywayEnum.JD_WALLET.getValue())) {
            //京东钱包开通处理
            final Integer provider = merchantProviderParams.getProvider();
            final Optional<McProviderDO> providerDO = mcProviderDAO.getByProvider(String.valueOf(provider));
            jdBiz.openSuccess(merchantProviderParams.getMerchant_sn(), providerDO.get().getAcquirer());
        }
        return Boolean.TRUE;
    }

    /**
     * 临时洗数据，用完即删
     */
    public boolean changeLklOpenUnionParams(MerchantProviderParams merchantProviderParams, String fee, boolean sync, String tradeAppId) {
        Assert.isTrue(Objects.equals(merchantProviderParams.getProvider(), ProviderEnum.PROVIDER_LKL_OPEN.getValue()), " provider must be 1034");
        String merchantSn = merchantProviderParams.getMerchant_sn();
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        Map requestParams = CollectionUtil.hashMap(
                MerchantConfig.MERCHANT_ID, merchantId,
                CommonModel.AGENT_NAME, "1034_*_false_true_0002",
                MerchantConfig.PAYWAY, merchantProviderParams.getPayway(),
                MerchantConfig.PROVIDER, merchantProviderParams.getProvider(),
                MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, merchantProviderParams.getProvider_merchant_id()
        );
        if (!StringUtils.isEmpty(fee)) {
            requestParams.put(TransactionParam.FEE_RATE, fee);
        }
        requestParams.put("merchant-provider-params", merchantProviderParams);
        requestParams.put("tradeAppId", tradeAppId);
        if (!updateMerchantConfigProviderCommon(requestParams)) {
            throw new CommonPubBizException(String.format("provider=%s 配置交易参数失败"));
        }
        return true;
    }


    /**
     * 切换交易参数，不检查是否和收单机构匹配
     *
     * @param merchantProviderParams
     * @param fee
     * @param sync                   是否使用同步方式配置appid
     * @param tradeAppId
     * @return
     */
    public boolean changeTradeParamsWithoutCheckAcquirer(MerchantProviderParams merchantProviderParams, String fee, boolean sync, String tradeAppId) {
        String merchantSn = merchantProviderParams.getMerchant_sn();
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        // 检查非线上微信子商户号结算id
        boolean pass = wxSettlementIdChangeBiz.checkSettlementId(merchantProviderParams);
        if (!pass && !merchantProviderParams.isOnlineParams() && !ContractRuleConstants.ZJTLCB_NORMAL_WEIXIN_RULE.equals(merchantProviderParams.getContract_rule())) {
            throw new CommonPubBizException("子商户号与商户行业不匹配，不允许切交易参数");
        }
        //当前商户该payWay下status=1的
        MerchantProviderParamsExample merchantProviderParamsExample = new MerchantProviderParamsExample();
        merchantProviderParamsExample.or()
                .andMerchant_snEqualTo(merchantProviderParams.getMerchant_sn())
                .andPaywayEqualTo(merchantProviderParams.getPayway())
//                .andIdNotEqualTo(merchantProviderParams.getId())
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andProviderNotEqualTo(merchantProviderParams.getPayway())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> upList = merchantProviderParamsMapper.selectByExample(merchantProviderParamsExample);
        // 支付源活动检查
        upList.forEach(u -> {
            if (StringUtils.isEmpty(u.getPay_merchant_id())) {
                return;
            }
            if (ThreadLocalUtil.isRefreshTradeParamsToPay()) {
                log.info("刷新交易参数到支付侧不做支付源活动检查, merchantSn:{}, paramsId:{}", merchantSn, u.getId());
                return;
            }
            boolean activityCheck = paywayActivityService.activityCheck(u.getPay_merchant_id());
            if (activityCheck) {
                throw new CommonPubBizException("当前默认子商户号支付源活动生效中，不可切换此子商号");
            }
        });
        // 切换交易参数 增加appid payAuthPath补偿配置
        if (PaywayEnum.WEIXIN.getValue().equals(merchantProviderParams.getPayway()) && WosaiCollectionUtils.isNotEmpty(upList)) {
            subAppIdSyns.addSubAppid(upList.get(0), merchantProviderParams, sync);
        }

        String merchantCity = configSupportService.getMerchantCity(merchantSn);
        int provider = merchantProviderParams.getProvider();
        int payWay = merchantProviderParams.getPayway();
        String channelNo = merchantProviderParams.getChannel_no();
        String agentName = agentAppidBiz.getAgentName(payWay, provider, channelNo, merchantCity);
        if (StringUtil.empty(agentName)) {
            log.error("agent name is null:{},{}", merchantProviderParams, merchantSn);
            throw new CommonPubBizException(String.format("获取 agentName 失败"));
        }
        Map requestParams = CollectionUtil.hashMap(
                MerchantConfig.MERCHANT_ID, merchantId,
                CommonModel.AGENT_NAME, agentName,
                MerchantConfig.PAYWAY, payWay,
                MerchantConfig.PROVIDER, provider,
                MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, merchantProviderParams.getProvider_merchant_id()
        );
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            requestParams.put(MerchantProviderTradeParams.ALIPAY_MERCHANT_ID, merchantProviderParams.getPay_merchant_id());
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            requestParams.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, merchantProviderParams.getPay_merchant_id());
        }
        if (provider == ProviderEnum.PROVIDER_UION_OPEN.getValue()) {
            //银联开放平台~~
            Map lakalaTradeParams = tradeConfigService.getLakalaTradeParams(merchantId);
            requestParams.put(TransactionParam.UNION_PAY_OPEN_TERM_ID, BeanUtil.getPropString(lakalaTradeParams, TransactionParam.LAKALA_TERM_ID));
        }
        //支付宝默认花呗开启
        if (PaywayEnum.ALIPAY.getValue() == payWay) {
            requestParams.put(TransactionParam.ALIPAY_HUABEI_STATUS, MerchantConfig.STATUS_OPENED);
        }
        if (!StringUtils.isEmpty(fee)) {
            requestParams.put(TransactionParam.FEE_RATE, fee);
        }
        if (provider == ProviderEnum.PROVIDER_HAIKE.getValue()) {
            requestParams.put("pay_merchant_id", merchantProviderParams.getPay_merchant_id());
        }
        requestParams.put("merchant-provider-params", merchantProviderParams);
        requestParams.put("tradeAppId", tradeAppId);

        // 配置merchant_config和merchant_app_config
        if (!updateMerchantConfigProviderCommon(requestParams)) {
            throw new CommonPubBizException(String.format("provider=%s 配置交易参数失败"));
        }

        applicationEventPublisher.publishEvent(new TradeParamsChangeEvent(this,
                merchantProviderParams,
                tradeAppId,
                merchantId,
                ThreadLocalUtil.getCheckSettlement()));

        return true;
    }

    private boolean updateMerchantConfigProviderCommon(Map params) {
        String merchantId = BeanUtil.getPropString(params, MerchantConfig.MERCHANT_ID);
        Map merchant = merchantService.getMerchant(merchantId);
        if (merchant == null) {
            throw new CommonPubBizException("商户不存在");
        }
        String agentName = BeanUtil.getPropString(params, CommonModel.AGENT_NAME);
        int payway = BeanUtil.getPropInt(params, MerchantConfig.PAYWAY, -1);
        int provider = BeanUtil.getPropInt(params, MerchantConfig.PROVIDER);
        String providerMerchantId = BeanUtil.getPropString(params, MerchantProviderTradeParams.PROVIDER_MERCHANT_ID);
        String payMerchantId;
        Integer alipayHuabeiStatus = null;
        MerchantProviderParams merchantProviderParams = (MerchantProviderParams) BeanUtil.getProperty(params, "merchant-provider-params");
        switch (payway) {
            case PayParamsModel.PAYWAY_ALIPAY2:
                payMerchantId = BeanUtil.getPropString(params, MerchantProviderTradeParams.ALIPAY_MERCHANT_ID);
                alipayHuabeiStatus = (Integer) params.get(TransactionParam.ALIPAY_HUABEI_STATUS);
                break;
            case PayParamsModel.PAYWAY_WEIXIN:
                payMerchantId = BeanUtil.getPropString(params, MerchantProviderTradeParams.WEIXIN_MERCHANT_ID);
                break;
            default:
                payMerchantId = merchantProviderParams.getPay_merchant_id();
                if (StringUtils.isEmpty(payMerchantId)) {
                    payMerchantId = BeanUtil.getPropString(params, MerchantProviderTradeParams.PROVIDER_MERCHANT_ID);
                }
                if (!StringUtil.empty(BeanUtil.getPropString(params, "pay_merchant_id"))) {
                    payMerchantId = BeanUtil.getPropString(params, "pay_merchant_id");
                }
                break;
        }
        String weixinAppid = BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_APP_ID);
        String weixinAppSecret = BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_APP_SECRET);
        String weixinMiniAppid = BeanUtil.getPropString(params, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
        String weixinMiniAppSecret = BeanUtil.getPropString(params, TransactionParam.WEIXIN_MINI_SUB_APP_SECRET);
        String merchantFee = BeanUtil.getPropString(params, TransactionParam.FEE_RATE);

        String lklTermId = BeanUtil.getPropString(params, TransactionParam.UNION_PAY_OPEN_TERM_ID);

        try {
            //获取业务方
            final String tradeAppId = BeanUtil.getPropString(params, "tradeAppId");
            //移动支付业务写入merchant_config,其他名称业务写入merchant_app_config,
            if (modifyMerchantConfigAppId.contains(tradeAppId)) {
                updateMerchantConfigProviderHandle(merchant, payway, provider, agentName, providerMerchantId, payMerchantId,
                        weixinAppid, weixinAppSecret, weixinMiniAppid, weixinMiniAppSecret, merchantFee,
                        alipayHuabeiStatus, lklTermId, merchantProviderParams);
            } else {
                updateMerchantAppConfigProviderHandle(merchant, payway, provider, agentName, providerMerchantId, payMerchantId,
                        weixinAppid, weixinAppSecret, weixinMiniAppid, weixinMiniAppSecret, merchantFee,
                        alipayHuabeiStatus, lklTermId, merchantProviderParams, tradeAppId);
            }
            //当tradeAppId是移动支付业务且收单机构是间连时也要更新merchant_app_config
            if (modifyMerchantConfigAppId.contains(tradeAppId) && subBizParamsBiz.supportSmart().contains(provider)) {
                updateMerchantAppConfigProviderHandle(merchant, payway, provider, agentName, providerMerchantId, payMerchantId,
                        weixinAppid, weixinAppSecret, weixinMiniAppid, weixinMiniAppSecret, merchantFee,
                        alipayHuabeiStatus, lklTermId, merchantProviderParams, tradeAppId);
            }
            //间连支付切换到银行对存量开通了储值,外卖,点单的商户merchant_app_config信息补全
            if (modifyMerchantConfigAppId.contains(tradeAppId) && !subBizParamsBiz.supportSmart().contains(provider)) {
                repairMerchantAppConfigProviderHandle(merchant, null);
            }
            String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
            supportService.removeCachedParams(merchantSn);
            //记录在本地sub_biz_params表中
            subBizParamsBiz.updateSubBizParams(merchantSn, tradeAppId, provider, merchantProviderParams);
            return true;
        } catch (Throwable e) {
            log.error("通道切换失败", e);
            throw e;
        }

    }

    private boolean updateMerchantConfigProviderHandle(Map merchant, int payway, int provider, String agentName, String providerMerchantId,
                                                       String payMerchantId, String weixinAppid, String weixinAppSecret, String weixinMiniAppid,
                                                       String weixinMiniAppSecret, String merchantFee, Integer alipayHuabeiStatus, String lklTermId,
                                                       MerchantProviderParams merchantProviderParams) {
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        //根据要求现在规则如下,直连不支持的sub_payway才会被切换到间连
        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        if (payway == -1 && provider == 0 && com.wosai.data.util.StringUtil.empty(agentName)) {
            log.error("updateMerchantConfigProviderHandle:当payway不为null, provider不为null时，必须设置agentName值");
            throw new CommonInvalidParameterException("agentName 不能为空");
        }
        if (merchantConfig == null) {
            merchantConfig = initMerchantConfig(merchantId, payway);
        }
        Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName(SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY);

        Map updateInfo = buildMerchantConfigUpdateInfo(merchantConfig, providerTradeParamsKey, payway, provider, agentName, providerMerchantId,
                payMerchantId, weixinAppid, weixinAppSecret, weixinMiniAppid,
                weixinMiniAppSecret, merchantFee, alipayHuabeiStatus, lklTermId, merchantProviderParams);


        //同步参数(如果费率不为null则同步费率)
        tradeConfigService.updateMerchantConfig(updateInfo);
        Map after = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        businessLogBiz.sendMerchantConfigBusinessLog(merchantConfig, after, "报备服务", "报备服务", "移动支付业务交易参数变更 " + ThreadLocalUtil.getChangeParamsRemark());
        return true;
    }

    private boolean updateMerchantAppConfigProviderHandle(Map merchant, int payway, int provider, String agentName, String providerMerchantId,
                                                          String payMerchantId, String weixinAppid, String weixinAppSecret, String weixinMiniAppid,
                                                          String weixinMiniAppSecret, String merchantFee, Integer alipayHuabeiStatus, String lklTermId,
                                                          MerchantProviderParams merchantProviderParams, String tradeAppId) {
        if (payway == -1 && provider == 0 && com.wosai.data.util.StringUtil.empty(agentName)) {
            log.error("updateMerchantConfigProviderHandle:当payway不为null, provider不为null时，必须设置agentName值");
            throw new CommonInvalidParameterException("agentName 不能为空");
        }
        //多业务不支持京东白条
        if (Objects.equals(payway, PaywayEnum.JD_WALLET.getValue())) {
            return Boolean.TRUE;
        }

        Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName(SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY);

        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        //白名单校验
        List whitelist = applicationApolloConfig.getMultiBusinessWhitelist();
        if (!CollectionUtils.isEmpty(whitelist) && whitelist.contains(merchantSn)) {
            log.info("更新merchant_app_config当前商户在白名单中 merchantSn:{},whitelist:{}", merchantSn, JSONObject.toJSONString(whitelist));
            return Boolean.TRUE;
        }
        //随着移动支付业务一起切换
        if (Lists.newArrayList(subBizParamsBiz.getPayTradeAppId(), subBizParamsBiz.getBankTradeAppId()).contains(tradeAppId)) {
            //这里获取所有当前的多业务, 过滤掉线上和跨城收款
            List<Map<String, Object>> payWayList = Optional.ofNullable(tradeConfigService.getMerchantAppConfigByMerchantIdAndApp(merchantId, null))
                    .orElseGet(ArrayList::new).stream()
                    .filter(appConfig -> Objects.equals(BeanUtil.getPropInt(appConfig, MerchantAppConfig.PAYWAY), payway))
                    .filter(appConfig -> !Objects.equals(subBizParamsBiz.getOnlinePaymentTradeAppId(), BeanUtil.getPropString(appConfig, MerchantAppConfig.APP_ID)) && !Objects.equals(subBizParamsBiz.getCrossCityPaymentTradeAppId(), BeanUtil.getPropString(appConfig, MerchantAppConfig.APP_ID)))
                    .collect(Collectors.toList());
            payWayList.stream().forEach(appConfig -> {
                updateMerchantAppConfig(payway, provider, agentName, providerMerchantId,
                        payMerchantId, weixinAppid, weixinAppSecret, weixinMiniAppid,
                        weixinMiniAppSecret, merchantFee, alipayHuabeiStatus, lklTermId,
                        merchantProviderParams, BeanUtil.getPropString(appConfig, MerchantAppConfig.APP_ID),
                        providerTradeParamsKey, merchantId, appConfig);
                //开通智慧经营发送消息 https://jira.wosai-inc.com/browse/CUA-6713(这里会重复发消息,已与交易确认支持幂等)
                subBizParamsBiz.sendTradeAppKafkaMsg(merchantSn, getAcquireByContractRule(merchantProviderParams.getContract_rule()), tradeAppId);
            });
            //用于弥补先于间连扫码开通的情况(这里会重复发消息,已与交易确认支持幂等)
            //merchant_config表已存在的appId有哪些
            Set<String> existAppIdList = payWayList.stream().map(appConfig -> BeanUtil.getPropString(appConfig, MerchantAppConfig.APP_ID)).collect(Collectors.toSet());
            //crm中开通成功的
            List<String> appOpenResults = getMerchantAppOpenResults(merchantId);
            if (!CollectionUtils.isEmpty(appOpenResults)) {
                //去除已经存在的
                appOpenResults.removeAll(existAppIdList);
                if (!CollectionUtils.isEmpty(appOpenResults) && tradeAppId.equalsIgnoreCase(subBizParamsBiz.getPayTradeAppId())) {
                    //构建交易基础参数
                    List<MerchantTradeConfig> byPassTradeConfigList = backAcquirerBiz.buildTradeConfig(Lists.newArrayList(merchantProviderParams));
                    appOpenResults.stream().forEach(appId -> {
                        // 饭卡只支持 海科+校园外卖
                        if (Objects.equals(payway, PaywayEnum.FOOD_CARD.getValue())
                                && !(Objects.equals(appId, subBizParamsBiz.getCampsFoodDelivery())
                                        && Objects.equals(provider, ProviderEnum.PROVIDER_HAIKE.getValue()))) {
                            return;
                        }
                        log.info("切换收单机构或者进件成功补充不存在的多业务参数,merchantSn:{},appId:{},ByPassTradeConfig:{},", merchantSn, appId, JSONObject.toJSONString(byPassTradeConfigList));
                        byPassTradeConfigList.forEach(config -> {
                            Map map = new MyObjectMapper().convertValue(config, Map.class);
                            map.put(MerchantAppConfig.APP_ID, appId);
                            map.put(MerchantAppConfig.MERCHANT_ID, merchantId);
                            tradeConfigService.createMerchantAppConfig(map);
                            //记录在本地sub_biz_params表中
                            subBizParamsBiz.updateSubBizParams(merchantSn, appId, provider, merchantProviderParams);
                        });
                        //开通智慧经营发送消息 https://jira.wosai-inc.com/browse/CUA-6713
                        subBizParamsBiz.sendTradeAppKafkaMsg(merchantSn, getAcquireByContractRule(merchantProviderParams.getContract_rule()), appId);
                    });
                }
            }
        } else {
            //以下是指定业务方配置对应的MerchantAppConfig数据
            //获取MerchantAppConfig表对应业务方的数据
            Map<String, Object> oldAppConfig = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, payway, tradeAppId);
            //构建交易基础参数
            List<MerchantTradeConfig> byPassTradeConfigList = backAcquirerBiz.buildTradeConfig(Lists.newArrayList(merchantProviderParams));
            byPassTradeConfigList.forEach(config -> {
                log.info("updateMerchantAppConfigProviderHandle,merchantSn:{},ByPassTradeConfig:{}", merchantSn, JSONObject.toJSONString(config));
                Map map = new MyObjectMapper().convertValue(config, Map.class);
                map.put(MerchantAppConfig.APP_ID, tradeAppId);
                map.put(MerchantAppConfig.MERCHANT_ID, merchantId);
                //补全交易参数信息
                if (MapUtils.isNotEmpty(oldAppConfig)) {
                    map.put(DaoConstants.ID, BeanUtil.getPropString(oldAppConfig, DaoConstants.ID));
                    //在MerchantAppConfig.PARAMS中追加最新的参数
                    Map params = MapUtils.getMap(oldAppConfig, MerchantAppConfig.PARAMS);
                    params.putAll(config.getParams());
                    map.put(MerchantAppConfig.PARAMS, params);
                    tradeConfigService.updateMerchantAppConfig(map);
                } else {
                    tradeConfigService.createMerchantAppConfig(map);
                }
            });
            Map appConfig = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, payway, tradeAppId);
            //这里又更新了一次是因为需要调用这个updateMerchantAppConfig记录日志
            updateMerchantAppConfig(payway, provider, agentName, providerMerchantId, payMerchantId, weixinAppid, weixinAppSecret, weixinMiniAppid, weixinMiniAppSecret, merchantFee, alipayHuabeiStatus, lklTermId, merchantProviderParams, tradeAppId, providerTradeParamsKey, merchantId, appConfig);
            log.info("updateMerchantAppConfigProviderHandle不存在appConfig自动补全,merchantSn,payway:{},tradeAppId:{}", merchantSn, payway, tradeAppId);
            if (!CollectionUtils.isEmpty(byPassTradeConfigList)) {
                //开通智慧经营发送消息 https://jira.wosai-inc.com/browse/CUA-6713
                subBizParamsBiz.sendTradeAppKafkaMsg(merchantSn, getAcquireByContractRule(merchantProviderParams.getContract_rule()), tradeAppId);
            }
            //如果移动支付业务为间连且多业务配置的也为间连通道时则使用移动支付套餐,不需要单独配置套餐
            ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
            String contractAcquire = Optional.ofNullable(contractStatus)
                    .filter(contract -> contract.getStatus() == ContractStatus.STATUS_SUCCESS)
                    .map(contract -> contract.getAcquirer()).orElseGet(String::new);
            List indirectAcquireList = mcAcquirerDAO.getIndirectAcquirerList();
            SubBizConfig subBizConfig = getSubBizConfig(tradeAppId);
            if ((Objects.nonNull(subBizConfig) && subBizConfig.isForceCombo()) || (!mcProviderDAO.isBankProvider(String.valueOf(provider)) && !indirectAcquireList.contains(contractAcquire))) {
                if (Objects.nonNull(subBizConfig) && subBizConfig.isPaywayCombo()) {
                    doComboAfterChangeParamByTradeAppIdAndPayway(tradeAppId, merchantSn, payway);
                } else {
                    doComboAfterChangeParamByTradeAppId(tradeAppId, merchantSn);
                }
            }
        }
        return true;
    }

    /**
     * 切换参数后根据业务方tradeAppId设置套餐
     *
     * @param tradeAppId 业务方ID
     * @param merchantSn 商户号
     */
    private void doComboAfterChangeParamByTradeAppId(String tradeAppId, String merchantSn) {
        SubBizConfig subBizConfig = getSubBizConfig(tradeAppId);
        if (Objects.isNull(subBizConfig)) {
            log.info("doComboAfterChangeParam empty,appName:{},merchantSn:{}", tradeAppId, merchantSn);
            return;
        }
        subBizParamsBiz.doCombo(merchantSn, subBizConfig);
    }

    /**
     * 切换参数后根据业务方tradeAppId设置套餐
     *
     * @param tradeAppId 业务方ID
     * @param merchantSn 商户号
     */
    private void doComboAfterChangeParamByTradeAppIdAndPayway(String tradeAppId, String merchantSn, int payway) {
        SubBizConfig subBizConfig = getSubBizConfig(tradeAppId);
        if (Objects.isNull(subBizConfig)) {
            log.info("doComboAfterChangeParam empty,appName:{},merchantSn:{}", tradeAppId, merchantSn);
            return;
        }
        subBizParamsBiz.doComboByPayway(merchantSn, subBizConfig, payway);
    }

    private SubBizConfig getSubBizConfig(String tradeAppId) {
        //设置费率套餐
        Map appIdSubBizMap = applicationApolloConfig.getAppIdSubBiz();
        List<Map> values = JSONArray.parseArray(JSONObject.toJSONString(appIdSubBizMap.values()), Map.class);
        Map<String, Map> tradeNameConfig = values.stream().collect(Collectors.toMap(sub -> MapUtils.getString(sub, "mappingTradeAppId"), sub -> sub, (k1, k2) -> k1));
        Map map = tradeNameConfig.get(tradeAppId);
        if (MapUtils.isEmpty(map)) {
            log.info("doComboAfterChangeParam empty,appName:{}", tradeAppId);
            return null;
        }
        return JSONObject.parseObject(JSON.toJSONString(map), SubBizConfig.class);
    }

    /**
     * 用于补偿存量已经开通储值或者智慧经营的商户在merchant_app_config中的参数
     *
     * @param merchant 商户信息
     * @param acquire  收单机构
     * @return
     */
    public Boolean repairMerchantAppConfigProviderHandle(Map merchant, String acquire) {
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        //调用crm查询商户成功开通了哪些业务
        List<String> tradeAppOpenResults = getMerchantAppOpenResults(merchantId);
        if (CollectionUtils.isEmpty(tradeAppOpenResults)) {
            log.info("repairMerchantAppConfigProviderHandle tradeAppOpenResults:{} 无需配置merchant_app_config,merchantSn:{}"
                    , JSONObject.toJSONString(tradeAppOpenResults), merchantSn);
            return Boolean.TRUE;
        }
        log.info("repairMerchantAppConfigProviderHandle,merchantSn:{}", merchantSn);
        //成功开通的"储值", "点单", "外卖",一个或者多个业务
        tradeAppOpenResults.forEach(appId -> {
            try {
                merchantProviderParamsService.openMultiTrade(merchantSn, appId);
            } catch (Exception e) {
                log.warn(String.format("商户号:%s,开通appId:%s,失败:%s", merchantSn, appId, e));
            }
        });
        return true;
    }

    /**
     * 查询商户在crm开通了哪些多业务
     *
     * @param merchantId
     * @return
     */
    public List<String> getMerchantAppOpenResults(String merchantId) {
        //获取apollo配置
        Map appIdSubBizMap = applicationApolloConfig.getAppIdSubBiz();
        //开通集合
        List<String> tradeAppIdList = Lists.newArrayList();
        appIdSubBizMap.forEach((k, v) -> {
            //默认查询当前商户前十个门店是否开通了"储值", "点单", "外卖"等业务
            ListResult listResult = commonAppInfoService.findAppInfos(new PageInfo(1, 10),
                    CollectionUtil.hashMap(AppInfoModel.MERCHANT_ID, merchantId
                            , AppInfoModel.STATUS, String.valueOf(AppInfoModel.STATUS_SUCCESS)
                            , AppInfoModel.APP_ID, k));
            List<Map> records = listResult.getRecords();
            //开通成功
            boolean success = records.stream().anyMatch(record -> Objects.equals(BeanUtil.getPropInt(record, AppInfoModel.STATUS), AppInfoModel.STATUS_SUCCESS));
            if (success) {
                SubBizConfig subBizConfig = JSON.parseObject(JSON.toJSONString(v), SubBizConfig.class);
                tradeAppIdList.add(subBizConfig.getMappingTradeAppId());
            }
        });
        return tradeAppIdList.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }


    private void updateMerchantAppConfig(int payway, int provider, String agentName, String providerMerchantId, String payMerchantId, String weixinAppid, String weixinAppSecret, String weixinMiniAppid, String weixinMiniAppSecret, String merchantFee, Integer alipayHuabeiStatus, String lklTermId, MerchantProviderParams merchantProviderParams, String tradeAppId, Map<String, Object> providerTradeParamsKey, String merchantId, Map appConfig) {
        Map updateInfo = buildMerchantConfigUpdateInfo(appConfig, providerTradeParamsKey, payway, provider, agentName, providerMerchantId, payMerchantId,
                weixinAppid, weixinAppSecret, weixinMiniAppid, weixinMiniAppSecret, merchantFee,
                alipayHuabeiStatus, lklTermId, merchantProviderParams);
        tradeConfigService.updateMerchantAppConfig(updateInfo);
        Map after = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, payway, tradeAppId);
        businessLogBiz.sendMerchantConfigBusinessLog(appConfig, after, "报备服务", "报备服务", String.format("%s交易参数变更 %s", subBizParamsBiz.getTradeAppNameById(tradeAppId), ThreadLocalUtil.getChangeParamsRemark()));
    }

    private Map buildMerchantConfigUpdateInfo(Map appConfig, Map<String, Object> providerTradeParamsKey, int payway, int provider, String agentName, String providerMerchantId,
                                              String payMerchantId, String weixinAppid, String weixinAppSecret, String weixinMiniAppid,
                                              String weixinMiniAppSecret, String merchantFee, Integer alipayHuabeiStatus, String lklTermId,
                                              MerchantProviderParams merchantProviderParams) {
        Map updateInfo = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(appConfig, DaoConstants.ID),
                MerchantAppConfig.PROVIDER, provider
        );

        if (WosaiStringUtils.isNotEmpty(BeanUtil.getPropString(appConfig, MerchantAppConfig.APP_ID))) {
            updateInfo.put(MerchantAppConfig.APP_ID, BeanUtil.getPropString(appConfig, MerchantAppConfig.APP_ID));
        }

        Map params = (Map) appConfig.get(MerchantConfig.PARAMS);
        if (params == null) {
            params = new HashMap();
        }
        String curWeixinMiniAppid = weixinMiniAppid;
        String curWeixinMiniAppSecret = weixinMiniAppSecret;

        // 如果以前配置过小程序appid, 切换到新通道也要配置
        if (payway == PaywayEnum.WEIXIN.getValue() && WosaiStringUtils.isEmpty(curWeixinMiniAppid)) {
            String oldProvider = BeanUtil.getPropString(appConfig, MerchantConfig.PROVIDER);
            String paramsKey = BeanUtil.getPropString(providerTradeParamsKey, oldProvider);
            curWeixinMiniAppid = BeanUtil.getPropString(params, paramsKey + ".weixin_mini_sub_appid");
            curWeixinMiniAppSecret = BeanUtil.getPropString(params, paramsKey + ".weixin_mini_sub_appsecret");
        }


        //如果正式，不替换对应的agentName
        for (String subPaywayColumn : subPaywayAgentNameColName.keySet()) {
            String agentNameColumn = subPaywayAgentNameColName.get(subPaywayColumn);
            String formalColumn = subPaywayFormalColName.get(subPaywayColumn);
            boolean formal = BeanUtil.getPropBoolean(appConfig, formalColumn, false);
            if (!formal) {
                updateInfo.put(agentNameColumn, agentName);
            }
        }

        if (!com.wosai.data.util.StringUtil.empty(merchantFee)) {
            updateInfo.put(MerchantConfig.B2C_FEE_RATE, merchantFee);
            updateInfo.put(MerchantConfig.C2B_FEE_RATE, merchantFee);
            updateInfo.put(MerchantConfig.WAP_FEE_RATE, merchantFee);
            updateInfo.put(MerchantConfig.MINI_FEE_RATE, merchantFee);
        }

        //花呗默认开启
        if (payway == PaywayEnum.ALIPAY.getValue() && alipayHuabeiStatus != null && MerchantConfig.STATUS_OPENED == alipayHuabeiStatus) {
            params.put(TransactionParam.ALIPAY_HUABEI_STATUS, MerchantConfig.STATUS_OPENED);
        }

        //清空设置到大商户下面的子商户号配置
        if (params != null && !params.isEmpty()) {
            if (providerTradeParamsKey != null) {
                for (String providerKey : providerTradeParamsKey.keySet()) {
                    //lakala 比较特殊，它还负责结算
                    if (!providerKey.equals(ProviderEnum.PROVIDER_LAKALA.getValue() + "")) {
                        params.remove(providerTradeParamsKey.get(providerKey));
                    }
                }
            }
            updateInfo.put(MerchantConfig.PARAMS, params);
        }

        if (payway != -1 && !com.wosai.data.util.StringUtil.empty(agentName) && provider != 0) {
            String paramsKey = getTradeParamsKeyByProvider(provider);
            Map<String, Object> tradeParams = (Map) BeanUtil.getNestedProperty(tradeConfigService.getAgentByName(agentName), String.format("%s.%s", Agent.PARAMS, paramsKey));
            if (tradeParams == null) {
                log.error("updateMerchantConfigProviderHandle:tradeParams is null");
                throw new CommonPubBizException(String.format("agent %s 中参数 %s 为空", agentName, paramsKey));
            }
            String tradeParamKey = BeanUtil.getPropString(providerTradeParamsKey, String.valueOf(provider));
            Map merchantTradeParams = getMerchantTradeParams(provider, payway, providerMerchantId, payMerchantId, weixinAppid,
                    weixinAppSecret, curWeixinMiniAppid, curWeixinMiniAppSecret, tradeParams, lklTermId, merchantProviderParams);
            if (merchantTradeParams == null) {
                throw new CommonPubBizException("构建交易参数失败");
            }
            params.put(tradeParamKey, merchantTradeParams);
            updateInfo.put(MerchantConfig.PARAMS, params);
        }
        return updateInfo;
    }

    public Map getMerchantTradeParams(int provider, int payway, String providerMerchantId, String payMerchantId, String weixinAppid,
                                      String weixinAppSecret, String weixinMiniAppid, String weixinMiniAppSecret,
                                      Map<String, Object> tradeParams, String lklTermId,
                                      MerchantProviderParams merchantProviderParams) {

        //通用银行导入情况
        Map<String, Object> config = batchTemplateApolloConfig.getSupportBankConfig();
        if (config.containsKey(Integer.toString(provider))) {
            Map bytes2Map = CommonUtil.bytes2Map(merchantProviderParams.getExtra());
            return (Map) bytes2Map.get("tradeParams");
        }
        switch (provider) {
            case PROVIDER_LKL_OPEN:
                if (payway == PaywayEnum.JD_WALLET.getValue()) {
                    byte[] extraByte = merchantProviderParams.getExtra();
                    if (ArrayUtils.isEmpty(extraByte)) {
                        throw new CommonInvalidParameterException("拉卡拉开放平台京东交易参数不存在");
                    }
                    final JSONObject object = JSON.parseObject(new String(extraByte));
                    final Map merchant = merchantService.getMerchantBySn(merchantProviderParams.getMerchant_sn());
                    final MerchantBusinessLicenseInfo licenseInfo = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(BeanUtil.getPropString(merchant, DaoConstants.ID), null);
                    object.put("merc_type", String.valueOf(licenseInfo.getType()));
                    return object;
                } else {
                    byte[] extraByte = merchantProviderParams.getExtra();
                    if (ArrayUtils.isEmpty(extraByte)) {
                        throw new CommonInvalidParameterException("拉卡拉开放平台交易参数不存在");
                    }
                    return JSON.parseObject(new String(extraByte));
                }
            case PROVIDER_NUCC:
                if (payway == PaywayEnum.ALIPAY.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.NUCC_ALIPAY_SUB_MCH_ID, payMerchantId);
                }
                if (payway == PaywayEnum.WEIXIN.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.NUCC_WEIXIN_SUB_MCH_ID, payMerchantId,
                            TransactionParam.NUCC_WEIXIN_SUB_APP_ID, weixinAppid,
                            TransactionParam.NUCC_WEIXIN_SUB_APP_SECRET, weixinAppSecret,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret);
                }
                if (payway == PaywayEnum.BESTPAY.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.SWIFTPASS_MCH_ID, payMerchantId);
                }
            case PROVIDER_UION_OPEN:
                if (payway == PaywayEnum.UNIONPAY.getValue()) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_OPEN_MCH_ID, payMerchantId,
                            TransactionParam.UNION_PAY_OPEN_TERM_ID, lklTermId);
                }
            case PROVIDER_UNIONPAY:
                if (payway == PaywayEnum.ALIPAY.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_ALIPAY_SUB_MCH_ID, payMerchantId);
                }
                if (payway == PaywayEnum.WEIXIN.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_WEIXIN_SUB_MCH_ID, payMerchantId,
                            TransactionParam.UNION_PAY_WEIXIN_SUB_APP_ID, weixinAppid,
                            TransactionParam.UNION_PAY_WEIXIN_SUB_APP_SECRET, weixinAppSecret,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret);
                }
            case PROVIDER_LKLWANMA:
                if (payway == PaywayEnum.ALIPAY.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.ALIPAY_PID, payMerchantId,
                            TransactionParam.LAKALA_WANMA_MCH_ID, providerMerchantId);
                }
                if (payway == PaywayEnum.WEIXIN.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.WEIXIN_SUB_MCH_ID, payMerchantId,
                            TransactionParam.LAKALA_WANMA_MCH_ID, providerMerchantId,
                            TransactionParam.WEIXIN_SUB_APP_ID, weixinAppid,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, weixinAppSecret,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret);
                }
            case PROVIDER_DIRECT_UNIONPAY:
                if (payway == PaywayEnum.ALIPAY.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID, payMerchantId,
                            TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId);
                }
                if (payway == PaywayEnum.WEIXIN.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_MCH_ID, payMerchantId,
                            TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId,
                            TransactionParam.WEIXIN_SUB_APP_ID, weixinAppid,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, weixinAppSecret,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret);
                }
            case PROVIDER_LKLORG:
                if (payway == PaywayEnum.ALIPAY.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID, payMerchantId,
                            TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId,
                            TransactionParam.TRADE_PARAMS_MERCHANT_NAME, merchantProviderParams.getMerchant_name());
                }
                if (payway == PaywayEnum.WEIXIN.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_MCH_ID, payMerchantId,
                            TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId,
                            TransactionParam.WEIXIN_SUB_APP_ID, weixinAppid,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, weixinAppSecret,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret,
                            TransactionParam.TRADE_PARAMS_MERCHANT_NAME, merchantProviderParams.getMerchant_name());
                }
            case PROVIDER_ICBC:
                return icbcTradeParams(merchantProviderParams, payMerchantId);
            case PROVIDER_TONGLIAN:
                //外部接口调用这里并不知道merchantProviderParams参数  这里兼容不存在查询一下
                if (merchantProviderParams == null && !com.wosai.mpay.util.StringUtils.isEmpty(payMerchantId)) {
                    merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId);
                }
                Map extra = CommonUtil.bytes2Map(merchantProviderParams.getExtra());
                Map tongLianTrade = null;
                if (payway == PaywayEnum.ALIPAY.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    tongLianTrade = WosaiMapUtils.getMap(extra, "tradeParams");
                }
                if (payway == PaywayEnum.WEIXIN.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    tongLianTrade = WosaiMapUtils.getMap(extra, "tradeParams");
                    tongLianTrade.put(TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid);
                    tongLianTrade.put(TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret);
                }
                if (payway == PaywayEnum.UNIONPAY.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    tongLianTrade = WosaiMapUtils.getMap(extra, "tradeParams");
                }
                if (tongLianTrade == null) {
                    throw new CommonInvalidParameterException("通联交易参数不存在");
                }

                return tongLianTrade;
            case PROVIDER_TONGLIAN_V2:
                return tonglianv2Params(merchantProviderParams);
            // 银联商务收单机构参数组装
            case PROVIDER_UMS:
                return umsTradeParams(merchantProviderParams, payMerchantId);

            case PROVIDER_PSBC:
                if (Objects.nonNull(merchantProviderParams) && !StringUtils.isEmpty(payMerchantId)) {
                    merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId);
                }

                if (payway == PaywayEnum.ALIPAY.getValue() && !StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.PSBCBANK_PROVIDER_MCH_ID, merchantProviderParams.getProvider_merchant_id(),
                            TransactionParam.PSBCBANK_ALIPAY_SUB_MCH_ID, payMerchantId
                    );
                }
                if (payway == PaywayEnum.WEIXIN.getValue() && !StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.PSBCBANK_PROVIDER_MCH_ID, merchantProviderParams.getProvider_merchant_id(),
                            TransactionParam.PSBCBANK_WEIXIN_SUB_MCH_ID, payMerchantId
                    );
                }
                if (payway == PaywayEnum.UNIONPAY.getValue() && !StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.PSBCBANK_PROVIDER_MCH_ID, merchantProviderParams.getProvider_merchant_id()
                    );
                }

                return CollectionUtil.hashMap(
                        TransactionParam.PSBCBANK_PROVIDER_MCH_ID, merchantProviderParams.getProvider_merchant_id()
                );
            case PROVIDER_CGB:
                return cgbTradeParams(merchantProviderParams, payMerchantId);
            case PROVIDER_LZB:
                return lzbTradeParams(merchantProviderParams, payMerchantId); 
            case PROVIDER_BCS:
                return bcsTradeParams(merchantProviderParams, payMerchantId);
            case PROVIDER_UMB:
                return umbTradeParams(merchantProviderParams, payMerchantId);
            case PROVIDER_CCB:
                return ccbTradeParams(merchantProviderParams, payMerchantId);
            case PROVIDER_PAB:
                return pabTradeParams(merchantProviderParams, payMerchantId);
            case PROVIDER_HXB:
                return hxbTradeParams(merchantProviderParams, payMerchantId);
            case PROVIDER_ZJTLCB:
                if (payway == PaywayEnum.ALIPAY.getValue()) {
                    return CollectionUtil.hashMap(
                            TransactionParam.ALIPAY_SUB_MCH_ID, payMerchantId,
                            TransactionParam.PROVIDER_MCH_ID, providerMerchantId);
                }
                if (payway == PaywayEnum.WEIXIN.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.WEIXIN_SUB_MCH_ID, payMerchantId,
                            TransactionParam.PROVIDER_MCH_ID, providerMerchantId,
                            TransactionParam.WEIXIN_SUB_APP_ID, weixinAppid,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, weixinAppSecret,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret);
                }
                if (payway == PaywayEnum.UNIONPAY.getValue()) {
                    return CollectionUtil.hashMap(
                            TransactionParam.PROVIDER_MCH_ID, providerMerchantId
                    );
                }
            case PROVIDER_HAIKE:
                if (payway == PaywayEnum.ALIPAY.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID, payMerchantId,
                            TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId,
                            TransactionParam.TRADE_PARAMS_MERCHANT_NAME, merchantProviderParams.getMerchant_name());
                }
                if (payway == PaywayEnum.WEIXIN.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_MCH_ID, payMerchantId,
                            TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId,
                            TransactionParam.WEIXIN_SUB_APP_ID, weixinAppid,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, weixinAppSecret,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret,
                            TransactionParam.TRADE_PARAMS_MERCHANT_NAME, merchantProviderParams.getMerchant_name());
                }
                if (payway == PaywayEnum.UNIONPAY.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    Map unionPayTradeParams = CollectionUtil.hashMap(
                            TransactionParam.MER_ID, payMerchantId,
                            TransactionParam.PROVIDER_MCH_ID, providerMerchantId,
                            TransactionParam.HAIKE_UNION_PAY_UNION_MER_NAME, merchantProviderParams.getMerchant_name());
                    if (WosaiStringUtils.isNotEmpty(merchantProviderParams.getAli_mcc())) {
                        unionPayTradeParams.put(TransactionParam.HAIKE_UNION_PAY_UNION_MCH_CAT_CODE, merchantProviderParams.getAli_mcc());
                    }
                    return unionPayTradeParams;
                }
                if (Objects.equals(payway, PaywayEnum.FOOD_CARD.getValue())) {
                    Map extraMap = CommonUtil.bytes2Map(merchantProviderParams.getExtra());
                    return CollectionUtil.hashMap(
                            FoodCardServiceImpl.LE_XIN_MCH_ID_KEY, MapUtils.getString(extraMap, FoodCardServiceImpl.LE_XIN_MCH_ID_KEY),
                            // FoodCardServiceImpl.MERCHANT_NAME, MapUtils.getString(extraMap, FoodCardServiceImpl.MERCHANT_NAME),
                            TransactionParam.PROVIDER_MCH_ID, providerMerchantId);

                }
            case PROVIDER_LEXIN:
                if (Objects.equals(payway, PaywayEnum.FOOD_CARD.getValue())) {
                    return CommonUtil.bytes2Map(merchantProviderParams.getExtra());
                }
            case PROVIDER_FUYOU:
                if (payway == PaywayEnum.ALIPAY.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID, payMerchantId,
                            TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId);
                }
                if (payway == PaywayEnum.WEIXIN.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_MCH_ID, payMerchantId,
                            TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId,
                            TransactionParam.WEIXIN_SUB_APP_ID, weixinAppid,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, weixinAppSecret,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret);
                }
                if (payway == PaywayEnum.UNIONPAY.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_OPEN_MCH_ID, payMerchantId,
                            TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId);
                }

                if (payway == PaywayEnum.JD_WALLET.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId);
                }
            case PROVIDER_GUOTONG:
                // TODO 国通 参数设置
                if (payway == PaywayEnum.ALIPAY.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID, payMerchantId,
                            TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId);
                }
                if (payway == PaywayEnum.WEIXIN.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_MCH_ID, payMerchantId,
                            TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId,
                            TransactionParam.WEIXIN_SUB_APP_ID, weixinAppid,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, weixinAppSecret,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret);
                }
                if (payway == PaywayEnum.UNIONPAY.getValue() && !com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_OPEN_MCH_ID, payMerchantId,
                            TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMerchantId);
                }
            default:
                String agentDefaultMchId = null;
                String agentWeixinSubAppid = null;
                String mchIdKey = null;
                for (String key : tradeParams.keySet()) {
                    if (key.endsWith(TransactionParam.SWIFTPASS_MCH_ID_SUFFIX)) {
                        mchIdKey = key;
                        agentDefaultMchId = BeanUtil.getPropString(tradeParams, key);
                    } else if (key.equals(TransactionParam.WEIXIN_SUB_APP_ID)) {
                        agentWeixinSubAppid = BeanUtil.getPropString(tradeParams, key);
                    }
                }
                if (payway == PaywayEnum.WEIXIN.getValue() && com.wosai.data.util.StringUtil.empty(agentWeixinSubAppid) && com.wosai.data.util.StringUtil.empty(agentDefaultMchId) && !com.wosai.data.util.StringUtil.empty(weixinAppid) && !com.wosai.data.util.StringUtil.empty(weixinMiniAppid)) {
                    //同一个商户有可能在同一个渠道报备了多次，区别在于报备的公众号不一样
                    if (!com.wosai.data.util.StringUtil.empty(providerMerchantId)) {
                        return CollectionUtil.hashMap(
                                mchIdKey, providerMerchantId,
                                TransactionParam.WEIXIN_SUB_APP_ID, weixinAppid,
                                TransactionParam.WEIXIN_SUB_APP_SECRET, weixinAppSecret,
                                TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid,
                                TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret
                        );
                    }
                }
                if (payway == PaywayEnum.ALIPAY.getValue()) {
                    return CollectionUtil.hashMap(mchIdKey, providerMerchantId);
                }
        }
        return null;
    }

    private Map bcsTradeParams(MerchantProviderParams merchantProviderParams, String payMerchantId) {
        if (merchantProviderParams == null && WosaiStringUtils.isNotEmpty(payMerchantId)) {
            merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId);
        }
        Map map = CommonUtil.bytes2Map(merchantProviderParams.getExtra());
        return putIfAbsent(merchantProviderParams, payMerchantId,
            (Map)map.computeIfAbsent("bcs_trade_params", t -> map.get("trade_params")));
    }

    private Map umbTradeParams(MerchantProviderParams merchantProviderParams, String payMerchantId) {
        if (merchantProviderParams == null && WosaiStringUtils.isNotEmpty(payMerchantId)) {
            merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId);
        }
        Map map = CommonUtil.bytes2Map(merchantProviderParams.getExtra());
        return putIfAbsent(merchantProviderParams, payMerchantId, (Map) map.computeIfAbsent("ztkx_trade_params", t -> map.get("trade_params")));
    }

    /**
     * @param provider
     * @return
     */
    private String getTradeParamsKeyByProvider(long provider) {
        Map<String, Object> map = tradeConfigService.getSystemConfigContentByName(SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY);
        String key = MapUtils.getString(map, provider + "");
        if (WosaiStringUtils.isNotEmpty(key)) {
            return key;
        }
        //新的数据都存在下面接口了
        Map<String, Object> metaMap = businssCommonService.getMetaProviderById((int) provider);
        return MapUtils.getString(metaMap, "trade_params_key");

    }

    private Map initMerchantConfig(String merchantId, int payway) {
        Map merchantConfig = CollectionUtil.hashMap(
                MerchantConfig.PAYWAY, payway,
                MerchantConfig.MERCHANT_ID, merchantId,
                MerchantConfig.B2C_FORMAL, false,
                MerchantConfig.C2B_FORMAL, false,
                MerchantConfig.WAP_FORMAL, false,
                MerchantConfig.MINI_FORMAL, false,
                MerchantConfig.PARAMS, new HashMap<>()
        );
        return tradeConfigService.createMerchantConfig(merchantConfig);
    }

    /**
     * 银联商务交易参数组装
     *
     * @param merchantProviderParams
     * @param payMerchantId
     * @return
     */
    private Map umsTradeParams(com.wosai.upay.job.model.DO.MerchantProviderParams merchantProviderParams, String payMerchantId) {
        if (merchantProviderParams == null && !com.wosai.mpay.util.StringUtils.isEmpty(payMerchantId)) {
            merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId);
        }
        Map map = CommonUtil.bytes2Map(merchantProviderParams.getExtra());
        return (Map) map.get("chinaums_trade_params");

    }

    private Map tonglianv2Params(com.wosai.upay.job.model.DO.MerchantProviderParams merchantProviderParams) {
        Map param = CollectionUtil.hashMap(TransactionParam.TL_SYB_CUS_ID, merchantProviderParams.getProvider_merchant_id());
        Integer payway = merchantProviderParams.getPayway();
        if (PaywayEnum.WEIXIN.getValue().equals(payway)) {
            param.put(CommonModel.WEIXIN_SUB_MCH_ID, merchantProviderParams.getPay_merchant_id());
        } else if (PaywayEnum.ALIPAY.getValue().equals(payway)) {
            param.put(CommonModel.ALIPAY_SUB_MCH_ID, merchantProviderParams.getPay_merchant_id());
        } else if (PaywayEnum.UNIONPAY.getValue().equals(payway)) {
            param.put(CommonModel.UNION_MCH_ID, merchantProviderParams.getPay_merchant_id());
        }
        return param;
    }

    /**
     * 广发交易参数组装
     *
     * @param merchantProviderParams
     * @param payMerchantId
     * @return
     */
    private Map cgbTradeParams(com.wosai.upay.job.model.DO.MerchantProviderParams merchantProviderParams, String payMerchantId) {
        if (merchantProviderParams == null && !com.wosai.mpay.util.StringUtils.isEmpty(payMerchantId)) {
            merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId);
        }
        Map map = CommonUtil.bytes2Map(merchantProviderParams.getExtra());
        return putIfAbsent(merchantProviderParams, payMerchantId, (Map) map.get("cgb_trade_params"));
    }

    /**
     * 补充对应的支付宝/微信参数
     *
     * @param merchantProviderParams
     * @param payMerchantId
     * @param tradeParams
     * @return
     */
    private Map putIfAbsent(MerchantProviderParams merchantProviderParams, String payMerchantId, Map tradeParams) {
        Integer payway = merchantProviderParams.getPayway();
        if (PaywayEnum.ALIPAY.getValue().equals(payway)) {
            tradeParams.putIfAbsent("alipay_sub_mch_id", payMerchantId);
        }
        if (PaywayEnum.WEIXIN.getValue().equals(payway)) {
            tradeParams.putIfAbsent("weixin_sub_mch_id", payMerchantId);
        }
        return tradeParams;
    }


    /**
     * 建行交易参数组装
     *
     * @param merchantProviderParams
     * @param payMerchantId
     * @return
     */
    private Map ccbTradeParams(MerchantProviderParams merchantProviderParams, String payMerchantId) {
        if (merchantProviderParams == null && !com.wosai.mpay.util.StringUtils.isEmpty(payMerchantId)) {
            merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId);
        }
        Map map = CommonUtil.bytes2Map(merchantProviderParams.getExtra());
        return putIfAbsent(merchantProviderParams, payMerchantId, (Map) map.get("ccb_trade_params"));
    }

    /**
     * 华夏交易参数组装
     *
     * @param merchantProviderParams
     * @param payMerchantId
     * @return
     */
    private Map hxbTradeParams(MerchantProviderParams merchantProviderParams, String payMerchantId) {
        if (merchantProviderParams == null && !com.wosai.mpay.util.StringUtils.isEmpty(payMerchantId)) {
            merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId);
        }
        Map map = CommonUtil.bytes2Map(merchantProviderParams.getExtra());
        return putIfAbsent(merchantProviderParams, payMerchantId, (Map) map.computeIfAbsent("hxbank_trade_params", t -> map.get("tradeParams")));
    }

    /**
     * 工商银行交易参数组装
     *
     * @param merchantProviderParams
     * @param payMerchantId
     * @return
     */
    private Map icbcTradeParams(com.wosai.upay.job.model.DO.MerchantProviderParams merchantProviderParams, String payMerchantId) {
        if (merchantProviderParams == null && !com.wosai.mpay.util.StringUtils.isEmpty(payMerchantId)) {
            merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId);
        }
        Map map = CommonUtil.bytes2Map(merchantProviderParams.getExtra());
        return putIfAbsent(merchantProviderParams, payMerchantId, (Map) map.computeIfAbsent("icbc_trade_params", t -> map.get("tradeParams")));
    }


    /**
     * 平安银行交易参数组装
     *
     * @param merchantProviderParams
     * @param payMerchantId
     * @return
     */
    private Map pabTradeParams(MerchantProviderParams merchantProviderParams, String payMerchantId) {
        if (merchantProviderParams == null && WosaiStringUtils.isNotEmpty(payMerchantId)) {
            merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId);
        }
        Map map = CommonUtil.bytes2Map(merchantProviderParams.getExtra());
        return putIfAbsent(merchantProviderParams, payMerchantId, (Map) map.computeIfAbsent("pab_up_trade_params", t -> map.get("tradeParams")));
    }

    /**
     * 泸州银行交易参数组装
     *
     * @param merchantProviderParams
     * @param payMerchantId
     * @return
     */
    private Map lzbTradeParams(MerchantProviderParams merchantProviderParams, String payMerchantId) {
        if (merchantProviderParams == null && WosaiStringUtils.isNotEmpty(payMerchantId)) {
            merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId);
        }
        Map map = CommonUtil.bytes2Map(merchantProviderParams.getExtra());
        //lzccb是交易侧事先定义好的,这里按照他们的命令来.
        return putIfAbsent(merchantProviderParams, payMerchantId, (Map) map.computeIfAbsent("lzccb_up_trade_params", t -> map.get("trade_params")));
    }

    /**
     * 根据规则获取对应的收单机构
     *
     * @param contractRule
     * @return
     */
    public String getAcquireByContractRule(String contractRule) {
        //mc_rule_group表contractRule与acquire对应关系
        final List<McContractRuleDO> ruleList = mcContractRuleDAO.listAllRule();
        //规则和acquire对应关系
        final Map<String, String> ruleAcquireMap = ruleList.parallelStream().collect(Collectors.toMap(x -> x.getRule(), x -> x.getAcquirer(), (val1, val2) -> val1));
        return BeanUtil.getPropString(ruleAcquireMap, contractRule);
    }

}
