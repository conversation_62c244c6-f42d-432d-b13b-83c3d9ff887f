package com.wosai.upay.job.refactor.biz.acquirer.tonglianv2;

import com.shouqianba.cua.enums.contract.AcquirerMerchantStatusEnum;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.biz.acquirer.TongLianV2AcquirerBiz;
import com.wosai.upay.job.constant.DevCodeConstants;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import com.wosai.upay.job.refactor.dao.PayBizApplyDAO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 通联V2收单处理门面
 *
 * <AUTHOR>
 * @date 2023/12/11 14:34
 */
@Service
public class TongLianV2AcquirerFacade extends AbstractAcquirerHandler {

    @Resource
    private TongLianV2MerchantInfoProcessor tongLianV2MerchantInfoProcessor;

    @Resource(type = TongLianV2AcquirerBiz.class)
    private TongLianV2AcquirerBiz tongLianV2AcquirerBiz;

    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.TONG_LIAN_V2;
    }

    /**
     * 判断商户所在收单机构的银行卡是否和收钱吧银行卡一致
     *
     * @param merchantSn 商户号
     * @return true-一致 false-不一致
     */
    @Override
    public boolean isBankCardConsistentWithSqb(String merchantSn) {
        return acquirerCommonTemplate.checkBankCardConsistentWithSqb(
                getTypeValue(), merchantSn,
                t -> tongLianV2MerchantInfoProcessor.getBankAccountNo(t).orElse(null),
                null
        );
    }

    @Override
    public boolean isFeeRateConsistentWithSqb(String merchantSn) {
        return tongLianV2MerchantInfoProcessor.isFeeRateConsistent(merchantSn);
    }

    /**
     * 获取商户所在收单机构的商户状态
     * todo 待重构 本次来不及
     *
     * @param merchantSn 商户号
     * @return 商户状态
     */
    @Override
    public AcquirerMerchantStatusEnum getAcquirerMerchantStatus(String merchantSn) {
        return tongLianV2AcquirerBiz.getAcquirerMchStatus(merchantSn) ? AcquirerMerchantStatusEnum.NORMAL : AcquirerMerchantStatusEnum.CLOSE;
    }
}
