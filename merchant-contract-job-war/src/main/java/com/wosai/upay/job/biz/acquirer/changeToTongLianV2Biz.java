package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.sales.core.service.IMerchantService;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.trade.service.bank.entity.FeeRateSnapshot;
import com.wosai.trade.service.bank.entity.request.RestoreFeeRateSnapshotRequest;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.constant.DevCodeConstants;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.refactor.dao.PayBizApplyDAO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.WeixinAppidConfig;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.weixin.SubdevConfigResp;
import com.wosai.upay.merchant.contract.service.TongLianService;
import com.wosai.upay.merchant.contract.service.WeixinService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description: changeToTongLianV2Biz
 * <AUTHOR>
 * @Date 2023/6/16 17:50
 **/

@Component("tonglianV2-AcquirerChangeBiz")
@Slf4j
public class changeToTongLianV2Biz extends AbstractIndirectAcquirerChangeBiz {

    @Autowired
    TongLianService tongLianService;

    @Autowired
    WeixinService weixinService;

    @Autowired
    private IMerchantService iMerchantService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    private PayBizApplyDAO payBizApplyDAO;

    private static final List<Integer> SUPPORT_PAYWAY_LIST = Arrays.asList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue(), PaywayEnum.UNIONPAY.getValue());
    ;

    @Override
    protected void acquirerSpecialCheck(String merchantSn, String merchantId, String sourceAcquirer, String targetAcquirer, boolean immediately) {
        if (!hasContract(merchantSn)) {
            throw new ContractBizException("通联收银宝未开通成功");
        }
        if (!checkOtherPayWay(merchantSn)) {
            throw new ContractBizException("支付宝/微信/云闪付交易参数不存在,不允许切换");
        }
    }

    private boolean hasContract(String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue())
                .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                .andDeletedEqualTo(false);
        return WosaiCollectionUtils.isNotEmpty(paramsMapper.selectByExampleWithBLOBs(example));
    }

    private boolean checkOtherPayWay(String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue())
                .andPaywayIn(Arrays.asList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue(), PaywayEnum.UNIONPAY.getValue()))
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        //是否存在微信参数
        final boolean matchWx = params.stream().anyMatch(param -> Objects.equals(param.getPayway(), PaywayEnum.WEIXIN.getValue()));
        //是否存在支付宝参数
        final boolean matchZfb = params.stream().anyMatch(param -> Objects.equals(param.getPayway(), PaywayEnum.ALIPAY.getValue()));
        //是否存在云闪付参数
        final boolean matchYzf = params.stream().anyMatch(param -> Objects.equals(param.getPayway(), PaywayEnum.UNIONPAY.getValue()));
        if (matchWx && matchZfb && matchYzf) {
            return true;
        }
        return false;
    }

    @Override
    public String getContractGroup(String merchantSn) {
        String merchantPath = getMerchantPath(merchantSn);
        if (WosaiStringUtils.isNotEmpty(merchantPath) && applicationApolloConfig.getSdTonglianV2Path().stream().anyMatch(merchantPath::startsWith)) {
            return ContractRuleConstants.CHANGE_TO_TONGLIANV2_SD_RULE_GROUP;
        }
        return ContractRuleConstants.CHANGE_TO_TONGLIANV2_RULE_GROUP;
    }

    @Override
    protected List<MerchantProviderParams> getDefaultChangeParams(McAcquirerChange change) {
        // 查找支付宝、云闪付、翼支付交易参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue())
                .andPaywayNotIn(Arrays.asList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue(), PaywayEnum.ACQUIRER.getValue()))
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime asc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        params.add(getAliParams(change));
        params.add(getWxParams(change));
        return params;
    }


    /**
     * 获取支付宝参数
     *
     * @param change
     * @return
     */
    private MerchantProviderParams getAliParams(McAcquirerChange change) {
        //获取最新的支付宝参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(getProviderCode(change.getTarget_acquirer()))
                .andPaywayEqualTo(PaywayEnum.ALIPAY.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        params = params.stream().filter(r -> !r.isOnlineParams()).collect(Collectors.toList());
        if (WosaiCollectionUtils.isNotEmpty(params)) {
            return params.get(0);
        }
        throw new CommonPubBizException(String.format("商户号:%s,没有找到支付宝参数", change.getMerchant_sn()));
    }

    /**
     * 获取微信参数
     *
     * @param change
     * @return
     */
    private MerchantProviderParams getWxParams(McAcquirerChange change) {
        // 先获取新渠道的微信参数
        MerchantProviderParamsExample wxExample = new MerchantProviderParamsExample();
        wxExample.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(getProviderCode(change.getTarget_acquirer()))
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andDeletedEqualTo(false);
        wxExample.setOrderByClause("ctime desc");
        List<MerchantProviderParams> wxParams = paramsMapper.selectByExampleWithBLOBs(wxExample);
        wxParams = wxParams.stream().filter(r -> !r.isOnlineParams()).collect(Collectors.toList());
        if (WosaiCollectionUtils.isEmpty(wxParams)) {
            throw new ContractBizException("缺少可用微信子商户号");
        }

        // 有多个微信交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        for (MerchantProviderParams wxParam : wxParams) {
            if (PayMchAuthStatusEnum.YES.getValue().equals(wxParam.getAuth_status())) {
                return wxParam;
            }
        }
        return wxParams.get(0);
    }


    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_TONGLIAN_V2.getValue();
    }

    private WeixinConfig getConfig(SubdevConfigResp subdevConfigResp, String merchantSn) {
        WeixinConfig weixinConfig = new WeixinConfig();
        weixinConfig.setWeixinMchId(getWeixinMchId(merchantSn));
        weixinConfig.setPayAuthPath(subdevConfigResp.getJsapi_path_list());
        List<WeixinAppidConfig> appidConfigs = subdevConfigResp.getAppid_config_list().stream().map(appidConfig -> {
            WeixinAppidConfig config = new WeixinAppidConfig();
            config.setSub_appid(appidConfig.getSub_appid());
            config.setSubscribe_appid(appidConfig.getSubscribe_appid());
            return config;
        }).collect(Collectors.toList());
        weixinConfig.setAppidConfigs(appidConfigs);
        return weixinConfig;
    }

    private String getWeixinMchId(String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        List<MerchantProviderParams> params = paramsMapper.selectByExample(example);
        if (WosaiCollectionUtils.isEmpty(params)) {
            throw new CommonPubBizException("未找到微信子商户号");
        } else {
            return params.get(0).getPay_merchant_id();
        }
    }

    /**
     * 获取微信间连在用参数
     *
     * @param merchantSn
     * @return
     */
    private MerchantProviderParams getWeixinInUseParams(String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andProviderNotEqualTo(ProviderEnum.WEI_XIN.getValue())
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = paramsMapper.selectByExample(example);
        return WosaiCollectionUtils.isNotEmpty(params) ? params.get(0) : null;
    }

    private String getMerchantPath(String merchantSn) {
        String organizationId = BeanUtil.getPropString(iMerchantService.getMerchantBySn(merchantSn), "organization_id");
        if (StringUtils.isBlank(organizationId)) {
            return StringUtils.EMPTY;
        }
        return MapUtils.getString(organizationService.getOrganization(organizationId), "path");
    }

    /**
     * 间连收单机构业务处理
     * @param change
     */
    @Override
    protected void targetAcquirerPostBiz(McAcquirerChange change) {
        // 如果通联收银宝申请正在切换收单机构中，就用这个申请单的套餐
        Optional<PayBizApplyDO> payBizApplyDO = payBizApplyDAO.selectByMerchantSnAndDevCode(change.getMerchant_sn(), DevCodeConstants.TONGLIAN_V2_DEV_CODE);
        if (payBizApplyDO.isPresent() && payBizApplyDO.get().isDetailEnabling()) {
            Map combo = CommonUtil.string2Map(payBizApplyDO.get().getFormBody());
            if (WosaiMapUtils.isNotEmpty(combo)) {
                applyCombo(change.getMerchant_sn(), combo);
                return;
            }
        }
        super.targetAcquirerPostBiz(change);
    }


    /**
     * 设置套餐
     * https://confluence.wosai-inc.com/pages/viewpage.action?pageId=572621307
     */
    protected void applyCombo(String merchantSn, Map combo) {
        long tradeComboId = BeanUtil.getPropLong(combo, "trade_combo_id");
        List<Map<String, Object>> config = JSONObject.parseArray(BeanUtil.getPropString(combo, "merchant_config"), Map.class)
                .stream()
                .filter(payWayConfig -> SUPPORT_PAYWAY_LIST.contains(BeanUtil.getPropInt(payWayConfig, "payway")))
                .map(map -> (Map<String, Object>) map)
                .collect(Collectors.toList()); // 使用toCollection收集

        // 支持阶梯费率
        Map<String, String> applyFeeRateMap = buildApplyFeeRateMap(config);
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(merchantSn)
                .setTradeComboId(tradeComboId)
                .setAuditSn("通联收银宝设置费率")
                .setApplyPartialPayway(Boolean.TRUE)
                .setApplyFeeRateMap(applyFeeRateMap);
        feeRateService.applyFeeRateOne(applyFeeRateRequest);
    }

    private Map<String, String> buildApplyFeeRateMap(List<Map<String, Object>> configs) {
        Map<String, String> applyFeeRateMap = new HashMap<>();
        for (Map config : configs) {
            if (WosaiCollectionUtils.isNotEmpty((Collection) config.get("ladder_fee_rates"))) {
                List<Map> waitLadder = new ArrayList<>();
                List<Map> ladderReq = (List<Map>) config.get("ladder_fee_rates");
                for (Map map : ladderReq) {
                    Integer min = org.apache.commons.collections.MapUtils.getInteger(map, "min");
                    Integer max = org.apache.commons.collections.MapUtils.getInteger(map, "max");
                    // 银行通道切换到间连再切回, configs里会没有rate信息,取bscFeeRate字段
                    String rate = org.apache.commons.collections.MapUtils.getString(map, "rate", org.apache.commons.collections.MapUtils.getString(map, "bscFeeRate"));
                    waitLadder.add(CollectionUtil.hashMap("min", min == null ? 0 : min, "max", max == null ? Integer.MAX_VALUE : max, "fee_rate", rate));
                }
                applyFeeRateMap.put(BeanUtil.getPropString(config, "payway"), JSON.toJSONString(CollectionUtil.hashMap("fee_type", "ladder", "value", waitLadder)));
            } else if (BeanUtil.getPropString(config, "rate").contains("以上")) {
                applyFeeRateMap.put(BeanUtil.getPropString(config, "payway"), JSON.toJSONString(CollectionUtil.hashMap("fee_type", "ladder", "value", getLadderFeeRateFromText(BeanUtil.getPropString(config, "rate")))));
            } else {
                applyFeeRateMap.put(BeanUtil.getPropString(config, "payway"), BeanUtil.getPropString(config, "rate"));
            }
        }
        return applyFeeRateMap;
    }

    /**
     * 有些阶梯费率格式是 300以下0.25, 300以上0.38
     * @param rate
     * @return
     */
    private static List<Map> getLadderFeeRateFromText(String rate) {
        List<Map> waitLadder = new ArrayList<>();
        String[] rates = rate.split("\\,");
        for (String s : rates) {
            if (s.contains("以下")) {
                String[] mins = s.split("以下");
                waitLadder.add(CollectionUtil.hashMap("min", 0, "max", Integer.valueOf(mins[0].trim()), "fee_rate", mins[1].trim()));
            } else {
                String[] maxs = s.split("以上");
                waitLadder.add(CollectionUtil.hashMap("min", Integer.valueOf(maxs[0].trim()), "max", Integer.MAX_VALUE, "fee_rate", maxs[1].trim()));
            }
        }
        return waitLadder;
    }

}