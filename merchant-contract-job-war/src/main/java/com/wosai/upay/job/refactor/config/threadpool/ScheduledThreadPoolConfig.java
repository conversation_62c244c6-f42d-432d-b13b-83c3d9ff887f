package com.wosai.upay.job.refactor.config.threadpool;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * <AUTHOR>
 * @date 2025/3/18
 */
@Configuration
public class ScheduledThreadPoolConfig {

    @Bean("contractStatusThreadPoolTaskScheduler")
    public ThreadPoolTaskScheduler threadPoolTaskScheduler() {
        ThreadPoolTaskScheduler threadPoolTaskScheduler = new ThreadPoolTaskScheduler();
        threadPoolTaskScheduler.setPoolSize(1);
        threadPoolTaskScheduler.setThreadNamePrefix("contractStatusThreadPoolTaskScheduler");
        threadPoolTaskScheduler.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskScheduler.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskScheduler.initialize();
        return threadPoolTaskScheduler;
    }

    @Bean("directStatusThreadPoolTaskScheduler")
    public ThreadPoolTaskScheduler directStatusThreadPoolTaskScheduler() {
        ThreadPoolTaskScheduler threadPoolTaskScheduler = new ThreadPoolTaskScheduler();
        threadPoolTaskScheduler.setPoolSize(5);
        threadPoolTaskScheduler.setThreadNamePrefix("directStatusThreadPoolTaskScheduler");
        threadPoolTaskScheduler.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成之后再关闭
        threadPoolTaskScheduler.setAwaitTerminationSeconds(30); // 最长等待时间
        threadPoolTaskScheduler.initialize();
        return threadPoolTaskScheduler;
    }

    @Bean("paymentModeChangeThreadPoolTaskScheduler")
    public ThreadPoolTaskScheduler paymentModeChangeThreadPoolTaskScheduler() {
        ThreadPoolTaskScheduler threadPoolTaskScheduler = new ThreadPoolTaskScheduler();
        threadPoolTaskScheduler.setPoolSize(1);
        threadPoolTaskScheduler.setThreadNamePrefix("paymentModeChangeThreadPoolTaskScheduler");
        threadPoolTaskScheduler.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskScheduler.setAwaitTerminationSeconds(30);
        threadPoolTaskScheduler.initialize();
        return threadPoolTaskScheduler;
    }

    @Bean("syncPreAuthThreadPoolTaskScheduler")
    public ThreadPoolTaskScheduler syncPreAuthThreadPoolTaskScheduler() {
        ThreadPoolTaskScheduler threadPoolTaskScheduler = new ThreadPoolTaskScheduler();
        threadPoolTaskScheduler.setPoolSize(2);
        threadPoolTaskScheduler.setThreadNamePrefix("syncPreAuthThreadPoolTaskScheduler");
        threadPoolTaskScheduler.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskScheduler.setAwaitTerminationSeconds(40);
        threadPoolTaskScheduler.initialize();
        return threadPoolTaskScheduler;
    }

    @Bean("syncSubappidThreadPoolTaskScheduler")
    public ThreadPoolTaskScheduler syncSubappidThreadPoolTaskScheduler() {
        ThreadPoolTaskScheduler threadPoolTaskScheduler = new ThreadPoolTaskScheduler();
        threadPoolTaskScheduler.setPoolSize(2);
        threadPoolTaskScheduler.setThreadNamePrefix("syncSubappidThreadPoolTaskScheduler");
        threadPoolTaskScheduler.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskScheduler.setAwaitTerminationSeconds(40);
        threadPoolTaskScheduler.initialize();
        return threadPoolTaskScheduler;
    }
}
