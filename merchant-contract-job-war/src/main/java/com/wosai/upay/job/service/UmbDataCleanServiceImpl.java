package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.ProviderTerminalMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.ProviderTerminal;
import com.wosai.upay.job.model.umb.UmbMerchantAcquirerInfo;
import com.wosai.upay.job.refactor.dao.MerchantAcquirerInfoDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantAcquirerInfoDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.UMBParam;
import com.wosai.upay.merchant.contract.service.UmbService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@AutoJsonRpcServiceImpl
@Component
@RequiredArgsConstructor
public class UmbDataCleanServiceImpl implements UmbDataCleanService {
    private final MerchantAcquirerInfoDAO merchantAcquirerInfoDAO;
    private final MerchantProviderParamsDAO merchantProviderParamsDAO;
    private final TradeConfigService tradeConfigService;
    private final SupportService supportService;
    private final ParamContextBiz paramContextBiz;
    private final MerchantService merchantService;
    private final MerchantProviderParamsMapper merchantProviderParamsMapper;
    private final ContractParamsBiz contractParamsBiz;
    private final UmbService umbService;
    private final ProviderTerminalMapper terminalMapper;


    @Override
    public Map<String, Object> setTradeConfigForUmbShareProfit(List<String> snList, boolean shareProfit) {
        long startTime = System.currentTimeMillis();
        int size;
        if (snList.isEmpty()) {
            List<MerchantAcquirerInfoDO> infoDOS = merchantAcquirerInfoDAO.getByAcquirerAndCtime("umb", "2025-06-18 23:20:15.390");
            size = infoDOS.size();
            for (MerchantAcquirerInfoDO infoDO : infoDOS) {
                String merchantSn = "";
                try {
                    //只处理成功进件的商户
                    String merchantInfo = infoDO.getMerchantInfo();
                    String statusInStr = JSON.parseObject(merchantInfo, UmbMerchantAcquirerInfo.class).getDetailed_status();
                    if (!"4".equals(statusInStr)) {
                        continue;
                    }
                    merchantSn = infoDO.getMerchantSn();
                    setConfigParam(merchantSn);
                } catch (Exception e) {
                    log.error("清洗中投分账交易参数错误. {} ", merchantSn, e);
                }
            }
        } else {
            size = snList.size();
            for (String merchantSn : snList) {
                try {
                    setConfigParamDirect(merchantSn, shareProfit);
                } catch (Exception e) {
                    log.error("清洗中投分账交易参数错误. {} ", merchantSn, e);
                }
            }
        }
        long duration = System.currentTimeMillis() - startTime;
        log.info("中投洗数据{}条耗时：{} 秒", size, duration / 1000);
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Object> fixHuaBeiForExistingMerchants(List<String> merchantUserList, int coreThreadNum) {
        long startTime = System.currentTimeMillis();
        int size;

        // 获取线程池配置
        ExecutorService executorService = Executors.newFixedThreadPool(coreThreadNum);
        List<Future<?>> futures = new ArrayList<>();

        if (merchantUserList.isEmpty()) {
            List<MerchantAcquirerInfoDO> infoDOS = merchantAcquirerInfoDAO.getByAcquirerAndCtime("umb", "2025-08-20 03:20:15.390");
            size = infoDOS.size();
            List<String> merchantSnList = infoDOS.stream().map(MerchantAcquirerInfoDO::getMerchantSn).collect(Collectors.toList());

            // 将商户列表分割为多个分片
            List<List<String>> partitions = partitionList(merchantSnList, size / coreThreadNum + 1);

            for (List<String> partition : partitions) {
                futures.add(executorService.submit(() -> processMerchantPartitionDirect(partition)));
            }
        } else {
            size = merchantUserList.size();

            // 将商户列表分割为多个分片
            List<List<String>> partitions = partitionList(merchantUserList, size / coreThreadNum + 1);

            for (List<String> partition : partitions) {
                futures.add(executorService.submit(() -> processMerchantPartitionDirect(partition)));
            }
        }

        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("[UmbDataCleanService]. 多线程处理商户时发生异常", e);
            }
        }

        // 关闭线程池
        executorService.shutdown();

        long duration = System.currentTimeMillis() - startTime;
        log.info("[UmbDataCleanService]. 中投花呗处理耗时：{} 条数据，耗时：{} 秒", size, duration / 1000);
        return Collections.emptyMap();
    }

    /**
     * 将列表分割为多个分片
     */
    private static <T> List<List<T>> partitionList(List<T> list, int size) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            partitions.add(new ArrayList<>(list.subList(i, Math.min(i + size, list.size()))));
        }
        return partitions;
    }


    private void processMerchantPartitionDirect(List<String> partition) {
        for (String merchantSn : partition) {
            try {
                processHuaBeiMerchantDirect(merchantSn);
            } catch (Exception e) {
                log.error("[UmbDataCleanService]. 设置中投指定商户上报花呗相关任务错误. {} ", merchantSn, e);
            }
        }
    }

    private void processHuaBeiMerchantDirect(String merchantSn) {
        Optional<MerchantAcquirerInfoDO> merchantSnAndAcquirer = merchantAcquirerInfoDAO.getByMerchantSnAndAcquirer(merchantSn, "umb");
        merchantSnAndAcquirer.ifPresent(infoDO -> processHuaBeiMerchant(merchantSn, infoDO.getAcquirerMerchantId()));
    }

    private void processHuaBeiMerchant(String merchantSn, String acquirerMerchantId) {
        MerchantProviderParams providerParams = merchantProviderParamsMapper.getParamsByProviderMerchantIdAndProviderAndPayWay(
                acquirerMerchantId,
                String.valueOf(ProviderEnum.PROVIDER_UMB.getValue()),
                String.valueOf(2));

        if (providerParams == null) {
            log.warn("[UmbDataCleanService].未查询到商户的交易参数,无法绑定终端: {}", merchantSn);
            return;
        }

        List<ProviderTerminal> terminalList = terminalMapper.selectByCondition(
                new ProviderTerminal()
                        .setMerchant_sn(merchantSn)
                        .setProvider(ProviderEnum.PROVIDER_UMB.getValue()));

        if (CollectionUtils.isEmpty(terminalList)) {
            log.warn("[UmbDataCleanService].未找到终端信息:{}", merchantSn);
            return;
        }

        UMBParam umbParam = contractParamsBiz.buildContractParamsByParams(providerParams, UMBParam.class);
        umbParam.setRule_group_id(providerParams.getRule_group_id());
        for (ProviderTerminal terminal : terminalList) {
            ContractResponse contractResponse = umbService.existingMerchantHuaBeiFix(
                    umbParam, 2, "1266000049390000", "2088011691288213", acquirerMerchantId, merchantSn, terminal.getProvider_terminal_id());
            if (!contractResponse.isSuccess()) {
                log.error("[UmbDataCleanService]. sn:{}.报备失败:{}", merchantSn, contractResponse.getMessage());
            }
        }
    }

    private void setConfigParamDirect(String merchantSn, boolean shareProfit) {
        Optional<MerchantProviderParamsDO> params = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(merchantSn, 1050, 0);
        Map merchantBySn = merchantService.getMerchantBySn(merchantSn);
        doSet(merchantSn, WosaiMapUtils.getString(merchantBySn, DaoConstants.ID), params, shareProfit);
    }

    private void setConfigParam(String merchantSn) {
        Optional<MerchantProviderParamsDO> params = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(merchantSn, 1050, 0);
        if (params.isPresent()) {
            // 判断是否分账.
            Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(merchantSn, null, false);
            Map<String, Object> merchant = (Map) contextParam.get("merchant");
            Map<String, Object> bankAccount = (Map) contextParam.get("bankAccount");
            Map<String, Object> businessLicense = (Map) contextParam.get("merchantBusinessLicense");
            //只有公司使用对私卡  和 个体但非法人 的情况,需要进行分润.
            //这里只允许同名换卡.
            boolean shareProfit = false;
            //个体但结算卡非法人
            if (BusinessLicenseTypeEnum.isIndividual((Integer) businessLicense.get(MerchantBusinessLicence.TYPE))) {
                boolean bankCardIsPrivateAndBankAccountHolderNotEqualBusinessLicenseLegalPerson = BankAccountTypeEnum.isPersonal((Integer) bankAccount.get(MerchantBankAccount.TYPE)) && !Objects.equals(bankAccount.get(MerchantBankAccount.HOLDER), businessLicense.get(MerchantBusinessLicence.LEGAL_PERSON_NAME));
                if (bankCardIsPrivateAndBankAccountHolderNotEqualBusinessLicenseLegalPerson) {
                    shareProfit = true;
                }
            }
            //公司使用对私卡
            if (BusinessLicenseTypeEnum.isEnterprise((Integer) businessLicense.get(MerchantBusinessLicence.TYPE))) {
                if (BankAccountTypeEnum.isPersonal((Integer) bankAccount.get(MerchantBankAccount.TYPE))) {
                    shareProfit = true;
                }
            }
            if (shareProfit) {
                doSet(merchantSn, WosaiMapUtils.getString(merchant, DaoConstants.ID), params, shareProfit);
            }
        }
    }

    private void doSet(String merchantSn, String merchantId, Optional<MerchantProviderParamsDO> params, boolean shareProfit) {
        tradeConfigService.updateZTKXTradeParams(
                merchantId,
                CollectionUtil.hashMap(
                        TransactionParam.LIQUIDATION_NEXT_DAY, !shareProfit,
                        TransactionParam.PLATFORM_MCH_ID, params.get().getChannelNo(),
                        TransactionParam.PROVIDER_MCH_ID, params.get().getPayMerchantId()
                )
        );
        supportService.removeCachedParams(merchantSn);
        String type = shareProfit ? "直清" : "间清";
        log.info("商户. {} 设置{}成功", merchantSn, type);
    }
}
