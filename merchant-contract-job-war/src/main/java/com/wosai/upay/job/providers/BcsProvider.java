package com.wosai.upay.job.providers;

import java.util.*;
import java.util.stream.Collectors;

import com.wosai.upay.merchant.contract.exception.ContractSysException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.aop.gateway.model.MerchantUserNoticeSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.aop.gateway.service.ClientSideSmsService;
import com.wosai.app.dto.MerchantUserInfo;
import com.wosai.app.service.MerchantUserService;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.bankDirect.BcsDirectBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.BankDirectApplyViewStatusEnum;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.refactor.dao.McChannelDAO;
import com.wosai.upay.job.refactor.dao.MerchantAcquirerInfoDAO;
import com.wosai.upay.job.refactor.model.entity.McChannelDO;
import com.wosai.upay.job.refactor.model.entity.MerchantAcquirerInfoDO;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.bcs.BcsCheckProcessEnum;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.service.BcsService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("bcs")
public class BcsProvider extends AbstractProvider {

    @Autowired
    private BcsDirectBiz bcsDirectBiz;

    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Autowired
    private MerchantAcquirerInfoDAO merchantAcquirerInfoDAO;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private BcsService bcsService;

    @Autowired
    private McChannelDAO mcChannelDAO;

    @Autowired
    private ClientSideSmsService clientSideSmsService;

    @Value("${bcs_dev_code}")
    private String bcsDevCode = "";

    @Value("${bcs_app_template_code}")
    private String bcsTemplateCode = "";

    @Autowired
    private ClientSideNoticeService clientSideNoticeService;

    @Autowired
    private MerchantUserService merchantUserService;

    @Autowired
    private MerchantProviderParamsBiz merchantProviderParamsBiz;

    @Autowired
    private MerchantProviderParamsMapper paramsMapper;

    @Autowired
    private BankService bankService;

    @Autowired
    private ContractEventMapper contractEventMapper;

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event,
        Map<String, Object> paramContext, ContractRule contractRule) {
        log.debug("BcsProvider.produceUpdateTaskByRule 生成更新任务, 商户SN: {}, 事件: {}, 参数: {}, 合同规则: {}", merchantSn,
            JSONObject.toJSONString(event), JSONObject.toJSONString(paramContext),
            JSONObject.toJSONString(contractRule));
        String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        int influPtask =
            AcquirerTypeEnum.BCS.getValue().equals(acquirer) || isSubBiz(merchantSn, AcquirerTypeEnum.BCS.getValue())
                ? contractRule.getUpdateInfluPtask() : 0;

        ContractSubTask subTask = new ContractSubTask().setMerchant_sn(merchantSn).setStatus_influ_p_task(influPtask)
            .setChannel(ProviderUtil.BCS_CHANNEL).setChange_config(0).setDefault_channel(contractRule.getDefault())
            .setPayway(contractRule.getPayway()).setContract_rule(contractRule.getRule())
            .setRule_group_id(event.getRule_group_id()).setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
            .setRetry(0);
        Integer taskType = null;
        if (PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
            if (ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == event.getEvent_type()) {
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                subTask.setTask_type(taskType);
            }
        }
        if (taskType == null) {
            return null;
        }
        return subTask;
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel,
        ContractSubTask contractSubTask) {
        try {
            if (!ProviderUtil.CONTRACT_TYPE_INSERT.equals(contractTask.getType())) {
                log.info("[BcsProvider] <processInsertTaskByRule> 任务类型不是新增入网, 任务类型: {}, 任务ID: {}",
                    contractTask.getType(), contractTask.getId());
                return null;
            }
            ContractResponse contractResponse;
            Integer payway = contractSubTask.getPayway();
            String merchantSn = contractTask.getMerchant_sn();
            Map<String, Object> contextParam =
                JSONObject.parseObject(contractTask.getEvent_context(), new TypeReference<Map<String, Object>>() {});
            String contractRule = contractSubTask.getContract_rule();
            log.info("BscProvider.processInsertTaskByRule: 开始处理BCS入网任务, 任务ID: {}, 商户SN: {}, 支付方式: {}, 合同规则: {}",
                contractTask.getId(), merchantSn, payway, contractRule);
            if (StringUtils.equals(contractRule, ContractRuleConstants.BCS_SIGN)
                && Objects.equals(contractSubTask.getStatus(), TaskStatus.PENDING.getVal())) {
                // 当sign子任务的状态为待处理时，前置处理
                contractResponse = bcsService.contractMerchantSign(contextParam);
                processSignInfo(contractTask, contractResponse, contextParam);
            } else if (StringUtils.equals(contractRule, ContractRuleConstants.BCS_SIGN)
                && Objects.equals(contractSubTask.getStatus(), TaskStatus.PROGRESSING.getVal())) {
                // 当sign子任务的状态为处理中时，查询签约状态
                contractResponse = queryMerchantStatus(contractTask, contractSubTask, contextParam);
                processBcsMerchantNo(contractTask, contractResponse, contextParam);
            } else if (StringUtils.equals(contractRule, ContractRuleConstants.BCS_CONTRACT)
                && Objects.equals(contractSubTask.getStatus(), TaskStatus.PENDING.getVal())) {
                // 处理收单渠道的入网任务
                contractResponse = processMerchantContract(contractTask, contextParam, merchantSn, contractSubTask);
            } else if (StringUtils.equals(contractRule, ContractRuleConstants.BCS_CONTRACT)
                && Objects.equals(contractSubTask.getStatus(), TaskStatus.PROGRESSING.getVal())) {
                // 商户触发进件，查询进件状态
                contractResponse = queryMerchantStatus(contractTask, contractSubTask, contextParam);
                // 判断商户进件的状态
                processCheckProcess(contractResponse, contractSubTask, contractTask);
            } else {
                // 处理微信、支付宝和云闪付等
                // 需要传递payWay、merchantSn和channelNo参数
                // 查询支付渠道对应的渠道号
                List<McChannelDO> mcChannelByAcquirer =
                    mcChannelDAO.getMcChannelByAcquirer(AcquirerTypeEnum.BCS.getValue(), payway);
                String channelNo =
                    CollectionUtils.isEmpty(mcChannelByAcquirer) ? "" : mcChannelByAcquirer.get(0).getChannelNo();
                contractResponse = bcsService.contractMerchantOtherPayWay(merchantSn, payway, channelNo);
                BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyByTaskId(contractTask.getId());
                // 将展示的状态流转为银行已审核
                bcsDirectBiz.recordViewProcess(bankDirectApply, BankDirectApplyViewStatusEnum.AUTHING.getValue(),
                    new Date());
            }
            log.info("BcsProvider.processInsertTaskByRule 任务处理完成, 任务ID: {}, 商户SN: {}, 支付方式: {}, 合同规则: {}, 结果: {}",
                contractTask.getId(), merchantSn, payway, contractRule, JSONObject.toJSONString(contractResponse));
            return contractResponse;
        } catch (Exception e) {
            log.error("[BcsProvider] <processInsertTaskByRule> 处理入网任务异常, 任务ID: {}", contractTask.getId(), e);
            ContractResponse contractResponse = new ContractResponse();
            contractResponse.setCode(501);
            contractResponse.setMessage(e.getMessage());
            return contractResponse;
        }
    }

    private void processCheckProcess(ContractResponse contractResponse, ContractSubTask contractSubTask,
        ContractTask contractTask) {
        Map<String, Object> tradeParam = contractResponse.getTradeParam();
        String checkProcess = (String)tradeParam.get("checkProcess");
        BcsCheckProcessEnum bcsCheckProcessEnum = BcsCheckProcessEnum.fromCode(checkProcess);
        if (bcsCheckProcessEnum == BcsCheckProcessEnum.PASS) {
            // 通过
            log.warn("BcsProvider.processCheckProcess 审核通过: {}, 子任务ID: {}", JSONObject.toJSONString(contractResponse),
                contractSubTask.getId());
            String mchtNo = (String)tradeParam.get("mchtNo");
            // 写入payWay是0的交易参数
            Map<String, Object> extra = new HashMap<>();
            String eventContext = contractTask.getEvent_context();
            Map<String, Object> contextMap =
                JSONObject.parseObject(eventContext, new TypeReference<Map<String, Object>>() {});
            String rate = (String)contextMap.getOrDefault("rate", "0.30");
            Map<String, String> tradeParamMap = new HashMap<>();
            tradeParamMap.put("fee_rate", rate);
            tradeParamMap.put("provider_mch_id", mchtNo);
            extra.put("trade_params", tradeParamMap);
            MerchantProviderParams params = new MerchantProviderParams().setId(UUID.randomUUID().toString())
                .setChannel_no(McConstant.RULE_GROUP_BCS).setProvider(ProviderEnum.PROVIDER_BCS.getValue())
                .setProvider_merchant_id(mchtNo).setPay_merchant_id(mchtNo)
                .setMerchant_sn(contractSubTask.getMerchant_sn())
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setContract_rule(ContractRuleConstants.BCS_CONTRACT).setRule_group_id(McConstant.RULE_GROUP_BCS)
                .setCtime(System.currentTimeMillis()).setMtime(System.currentTimeMillis())
                .setExtra(CommonUtil.map2Bytes(extra));
            paramsMapper.insertSelective(params);
            return;
        } else if (bcsCheckProcessEnum == BcsCheckProcessEnum.CANCLE) {
            log.warn("BcsProvider.processCheckProcess 审核取消: {}, 子任务ID: {}", JSONObject.toJSONString(contractResponse),
                contractSubTask.getId());
            throw new RuntimeException("审核取消, 子任务ID: " + contractSubTask.getId());
        }
        // 审核状态未通过，需要在response中写入contractId
        log.info("BcsProvider.processCheckProcess 审核状态: {}, 子任务ID: {}", bcsCheckProcessEnum.getDescription(),
            contractSubTask.getId());
        tradeParam.put("contractId", contractSubTask.getContract_id());
    }

    private void processBcsMerchantNo(ContractTask contractTask, ContractResponse contractResponse,
        Map<String, Object> contextParam) {
        if (contractResponse.isBusinessFail()) {
            // 业务异常，不处理
            log.warn("[BcsProvider] <processBcsMerchantNo> 查询签约任务处理失败, 任务ID: {}, 错误信息: {}", contractTask.getId(),
                contractResponse.getMessage());
            return;
        }
        Map<String, Object> tradeParam = contractResponse.getTradeParam();
        String mchtNo = (String)tradeParam.get("mchtNo");
        if (StringUtils.isEmpty(mchtNo)) {
            log.warn("[BcsProvider] <processBcsMerchantNo> 查询签约任务没有返回长沙银行商户信息, 任务ID: {}", contractTask.getId());
            return;
        }
        // 写入获取到的长沙银行商户号
        contextParam.put("mchtNo", mchtNo);
        ContractTask update = new ContractTask();
        update.setId(contractTask.getId());
        update.setEvent_context(JSONObject.toJSONString(contextParam));
        contractTaskMapper.updateByPrimaryKey(update);
        // 更新db中的contractId为长沙银行的mchtNo
        List<ContractSubTask> contractSubTasks = contractSubTaskMapper.selectByPTaskId(contractTask.getId());
        List<ContractSubTask> subTaskCollect = contractSubTasks.stream().filter(
            contractSubTask -> StringUtils.equals(contractSubTask.getContract_rule(), ContractRuleConstants.BCS_SIGN)
                || StringUtils.equals(contractSubTask.getContract_rule(), ContractRuleConstants.BCS_CONTRACT))
            .collect(Collectors.toList());
        subTaskCollect.forEach(contractSubTask -> {
            ContractSubTask updateSub = new ContractSubTask();
            updateSub.setId(contractSubTask.getId());
            updateSub.setContract_id(mchtNo);
            contractSubTaskMapper.updateByPrimaryKey(updateSub);
        });
    }

    public void processSignInfo(ContractTask contractTask, ContractResponse contractResponse,
        Map<String, Object> contextParam) {
        log.info("BcsProvider.processSignInfo 处理签约任务, 任务ID: {}, 签约响应: {}", contractTask.getId(),
            JSONObject.toJSONString(contractResponse));
        // 获取到多张照片的content_id和signUrl
        Map<String, Object> tradeParam = contractResponse.getTradeParam();
        if (tradeParam == null || tradeParam.isEmpty()) {
            log.warn("[BcsProvider] <processSignInfo> 签约任务没有返回签约信息, 任务ID: {}", contractTask.getId());
            return;
        }
        // 将签约的照片信息存入contextParam中，包括我方生成的进件id
        contextParam.put("signInfo", tradeParam);
        contractTask.setEvent_context(JSONObject.toJSONString(contextParam));
        // 发送签约短信给用户
        String signUrl = (String)tradeParam.get("signUrl");
        Object merchant = contextParam.get("merchant");
        Map<String, Object> merchantMap =
            JSONObject.parseObject(JSONObject.toJSONString(merchant), new TypeReference<Map<String, Object>>() {});
        sendSignUrl2App(signUrl, merchantMap);
        // 将展示的状态流转为已提交，待签约
        BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyByTaskId(contractTask.getId());
        bcsDirectBiz.recordViewProcess(bankDirectApply, BankDirectApplyViewStatusEnum.DISTRIBUTING.getValue(),
            new Date());
        // 更新任务的event_context
        ContractTask update = new ContractTask();
        update.setId(contractTask.getId());
        update.setEvent_context(JSONObject.toJSONString(contextParam));
        contractTaskMapper.updateByPrimaryKey(update);
    }

    public void sendSignUrl2App(String signUrl, Map<String, Object> merchantMap) {
        final MerchantUserNoticeSendModel sendModel = new MerchantUserNoticeSendModel();
        String id = (String)merchantMap.get("id");
        String merchantSn = (String)merchantMap.get("sn");
        MerchantUserInfo merchantUserInfo = merchantUserService.getSuperAdminByMerchantId(id);
        if (merchantUserInfo == null) {
            log.warn("商户用户信息不存在, 商户ID: {}, 商户SN: {}", id, merchantSn);
            return;
        }
        sendModel.setDevCode(bcsDevCode);
        sendModel.setTemplateCode(bcsTemplateCode);
        sendModel.setMerchantUserId(merchantUserInfo.getMerchant_user_id());
        sendModel.setTimestamp(System.currentTimeMillis());
        // 处理url
        String protocol = "http://";
        String protocolHttps = "https://";
        String bcsSignUrl = "";
        // 检查URL是否以指定协议开头
        if (signUrl.startsWith(protocol)) {
            // 从协议长度之后开始截取，得到目标部分
            bcsSignUrl = signUrl.substring(protocol.length());
        } else if (signUrl.startsWith(protocolHttps)) {
            bcsSignUrl = signUrl.substring(protocolHttps.length());
        }
        // 需要传的数据
        sendModel.setData(CollectionUtil.hashMap("bcsSignUrl", bcsSignUrl));
        log.info("长沙银行给收钱吧app发送通知参数{},商户号:{}", JSONObject.toJSONString(sendModel), merchantSn);
        clientSideNoticeService.sendToMerchantUser(sendModel);
    }

    private ContractResponse processMerchantContract(ContractTask contractTask, Map<String, Object> contextParam,
        String merchantSn, ContractSubTask contractSubTask) {
        ContractResponse contractResponse;
        contractResponse = bcsService.contractMerchant(contextParam);
        // 需要传递contextParam参数
        if (contractResponse.isSuccess()) {
            // 商户进件成功，待审核
            Map<String, Object> tradeParam = new HashMap<>();
            // 将contractId写入到response中,保证subTask不结束
            tradeParam.put("contractId", contractSubTask.getContract_id());
            contractResponse.setTradeParam(tradeParam);
            // 避免重复进件，需要保存商户的收单机构
            String acquirerMerchantId = contractSubTask.getContract_id();
            MerchantAcquirerInfoDO merchantAcquirerInfoDO = new MerchantAcquirerInfoDO();
            merchantAcquirerInfoDO.setMerchantSn(merchantSn);
            merchantAcquirerInfoDO.setAcquirer(AcquirerTypeEnum.BCS.getValue());
            merchantAcquirerInfoDO.setAcquirerMerchantId(acquirerMerchantId);
            merchantAcquirerInfoDAO.insertOrUpdateOne(merchantAcquirerInfoDO);
            // 将展示的状态流转为已签约
            BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyByTaskId(contractTask.getId());
            bcsDirectBiz.recordViewProcess(bankDirectApply, BankDirectApplyViewStatusEnum.SIGNED_AUDITING.getValue(),
                new Date());
        }
        return contractResponse;
    }

    private ContractResponse queryMerchantStatus(ContractTask contractTask, ContractSubTask contractSubTask,
        Map<String, Object> contextParam) {
        ContractResponse contractResponse;
        // 当子任务的状态为1时，查询状态
        // 查询状态是有7天的有效期的，超过7天后商家进件会被认为是失败
        Date createAt = contractSubTask.getCreate_at();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(createAt);
        calendar.add(Calendar.DAY_OF_YEAR, 7);
        Date sevenDaysLater = calendar.getTime();
        Date currentDate = new Date();
        boolean isAfter = currentDate.after(sevenDaysLater);
        if (isAfter) {
            // 超过7天后，商户进件会被认为是失败
            log.warn("[BcsProvider] <queryMerchantSignStatus> 任务超过7天，商户进件失败, 子任务ID: {}", contractSubTask.getId());
            contractResponse = new ContractResponse();
            contractResponse.setMessage("任务超过7天，商户进件失败");
            contractResponse.setCode(501);
            return contractResponse;
        }
        return bcsService.queryMerchantStatus(contextParam);
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel,
        ContractSubTask sub) {
        log.debug(
            "BcsProvider.processUpdateTaskByRule 处理更新任务, 任务ID: {}, 商户SN: {},contractTask:{}, contractChannel:{}, sub:{} ",
            contractTask.getId(), contractTask.getMerchant_sn(), JSONObject.toJSONString(contractTask),
            JSONObject.toJSONString(contractChannel), JSONObject.toJSONString(sub));
        try {
            Integer taskType = sub.getTask_type();
            if (Objects.equals(taskType, ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS)) {
                ContractEvent contractEvent = contractEventMapper.selectByTaskId(contractTask.getId());
                if (contractEvent == null) {
                    log.error("BcsProvider.processUpdateTaskByRule 未找到对应的事件, 任务ID: {}", contractTask.getId());
                    throw new Exception(
                        "BcsProvider.processUpdateTaskByRule 处理更新任务失败, 未找到对应的事件, 任务ID: " + contractTask.getId());
                }
                String requestBody = contractEvent.getEvent_msg();
                JSONObject jsonObject = JSONObject.parseObject(requestBody);
                JSONObject sourceObject = jsonObject.getJSONObject("source");
                Map<String, Object> map = new HashMap<>();
                map.put("id", sourceObject.getString("id"));
                map.put("operator", sourceObject.getString("operator"));
                map.put("platform", sourceObject.getString("platform"));
                map.put("remark", sourceObject.getString("remark"));
                bankService.replaceMerchantBankAccount(map);
                ContractResponse contractResponse = ContractResponse.builder().code(200).message("更新结算账户信息成功").build();
                contractResponse.setTradeParam(new HashMap<>());
                return contractResponse;
            }
            ContractResponse contractResponse = ContractResponse.builder().code(200).message("更新结算账户信息成功").build();
            contractResponse.setTradeParam(new HashMap<>());
            return contractResponse;
        } catch (Exception e) {
            log.error("BcsProvider.processUpdateTaskByRule 处理更新任务异常, 任务ID: {}", contractTask.getId(), e);
            ContractResponse contractResponse = new ContractResponse();
            contractResponse.setCode(401);
            contractResponse.setMessage(e.getMessage());
            return contractResponse;
        }
    }

    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("result_code", "1");
        resultMap.put("message", "配置成功");
        return resultMap;
    }
}
