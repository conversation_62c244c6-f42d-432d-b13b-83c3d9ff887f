package com.wosai.upay.job.refactor.orchestrator.account;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.dto.BankAccountSimpleInfoBO;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerSharedAbility;
import com.wosai.upay.job.refactor.biz.acquirer.lklv3.LklV3MerchantInfoProcessor;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.task.rotational.RotationalTask;
import com.wosai.upay.job.refactor.task.rotational.builder.AcquirerAccountChangePollingTaskBuilder;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalTaskContext;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalSubTaskTypeEnum;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.merchant.contract.model.lklV3.MerAccountResp;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

/**
 * 收单机构账户变更记录
 * 
 * 功能：
 * 1. 记录收单机构账户变更信息
 * 2. 创建账户变更轮询任务
 * 3. 支持多种收单机构的扩展
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Component
@Slf4j
public class AcquirerAccountChangeCoordinator {

    @Autowired
    private MerchantBankService merchantBankService;

    @Resource
    private LklV3MerchantInfoProcessor lklV3MerchantInfoProcessor;

    @Autowired
    private LklV3Service lklV3Service;

    @Resource
    private ContractParamsBiz contractParamsBiz;

    @Resource
    private RotationalTask rotationalTask;
    
    @Resource
    private com.wosai.upay.job.refactor.dao.InternalScheduleMainTaskDAO internalScheduleMainTaskDAO;

    @Resource
    private RedissonClient redissonClient;


    @Resource
    protected MerchantBasicInfoBiz merchantBasicInfoBiz;

    @Resource
    private AcquirerFacade acquirerFacade;

    /**
     * 记录收单机构账户变更并创建轮询任务
     */
    public void recordAcquirerAccountChangeAndCreatePollingTask(String merchantSn,
                                                                String acquirer,
                                                                String providerMerchantId,
                                                                Long contractTaskId,
                                                                Long contractSubTaskId,
                                                                Map<?, ?> newAccountInfo) {
        RLock lock = null;
        try {
            String lockKey = "merchant-contract-job:rotational_task:create:" + merchantSn + ":" + contractSubTaskId + ":" + RotationalSubTaskTypeEnum.ACQUIRER_ACCOUNT_CHANGE_POLLING.getValue();
            lock = redissonClient.getLock(lockKey);
            if (!lock.tryLock()) {
                log.warn("Acquire polling task create lock failed, key={}, skip creating", lockKey);
                return;
            }

            // 去重：同商户 + 同子任务类型 + 同 rotationId(=contractSubTaskId)
            if (alreadyHasSamePollingTask(merchantSn, String.valueOf(contractSubTaskId))) {
                log.info("Duplicate polling task detected, skip creating. merchantSn={}, rotationId={}, subTaskType={}", merchantSn, contractSubTaskId, RotationalSubTaskTypeEnum.ACQUIRER_ACCOUNT_CHANGE_POLLING.getValue());
                return;
            }

            String merchantId = merchantBasicInfoBiz.getMerchantInfoBySn(merchantSn).getId();
            BankAccountSimpleInfoBO oldBankAccount =  getOldAccountInfo(acquirer, providerMerchantId, merchantId, merchantSn);
            if (Objects.isNull(oldBankAccount)) {
                log.warn("获取当前收单机构银行卡信息失败，不记录本次换卡变更, merchantSn:{}, acquirer:{}, contractSubTask={}", merchantSn, acquirer, contractSubTaskId);
                return;
            }
            BankAccountSimpleInfoBO newBankAccount = buildBankAccountInfo(newAccountInfo);
            createPollingTask(merchantSn, contractTaskId, contractSubTaskId, acquirer, providerMerchantId, oldBankAccount, newBankAccount);
        } catch (Exception e) {
            log.error("Record acquirer account change and create polling task failed, merchantSn={}, acquirer={}, contractSubTask={}", contractSubTaskId, acquirer,contractSubTaskId, e);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 判断是否已存在相同轮询任务（基于 merchantSn + subTaskType + rotationId）
     */
    private boolean alreadyHasSamePollingTask(String merchantSn, String rotationId) {
        try {
            List<InternalScheduleMainTaskDO> list = internalScheduleMainTaskDAO.listBySnAndType(merchantSn, InternalScheduleTaskTypeEnum.ROTATIONAL_TASK.getValue());
            if (CollectionUtils.isEmpty(list)) {
                return false;
            }
            for (InternalScheduleMainTaskDO main : list) {
                try {
                    RotationalTaskContext ctx = JSON.parseObject(main.getContext(), RotationalTaskContext.class);
                    if (ctx == null) {
                        continue;
                    }
                    if (ctx.getSubTaskTypeEnum() == null || ctx.getRotationId() == null) {
                        continue;
                    }
                    if (Objects.equals(ctx.getSubTaskTypeEnum().getValue(), RotationalSubTaskTypeEnum.ACQUIRER_ACCOUNT_CHANGE_POLLING.getValue())
                            && Objects.equals(ctx.getRotationId(), rotationId)) {
                        return true;
                    }
                } catch (Exception ignore) {
                    // ignore broken context
                }
            }
            return false;
        } catch (Exception e) {
            log.warn("Check duplicate polling task failed, merchantSn={}, rotationId={}, subType={}.", merchantSn, rotationId,
                    RotationalSubTaskTypeEnum.ACQUIRER_ACCOUNT_CHANGE_POLLING.getValue(), e);
            return false;
        }
    }

    /**
     * 根据收单机构类型获取旧账户信息
     */
    private BankAccountSimpleInfoBO getOldAccountInfo(String acquirer, String providerMerchantId,
                                                      String merchantId, String merchantSn) {
        if (AcquirerTypeEnum.LKL_V3.getValue().equals(acquirer)) {
            return getLklV3OldAccountInfo(providerMerchantId, merchantId, merchantSn);
        }
        // 其他收单机构：直接从收单机构取当前卡（抽象层默认回退为系统默认卡）
        AcquirerSharedAbility ability = acquirerFacade.getSharedAbilityByAcquirer(acquirer).orElse(null);
        if (ability == null) {
            log.warn("No acquirer ability found for acquirer={}, merchantSn={}", acquirer, merchantSn);
            return null;
        }
        return ability.getCurrentBankAccountFromAcquirer(merchantSn);
    }

    /**
     * 获取拉卡拉V3的旧账户信息
     */
    private BankAccountSimpleInfoBO getLklV3OldAccountInfo(String providerMerchantId,
                                                           String merchantId, String merchantSn) {
        try {
            final LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
            MerAccountResp merAccountResp = lklV3Service.queryMerAccount(providerMerchantId, lklV3Param);
            String oldAcctNo = merAccountResp.getAcctNo();
            
            ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                    CollectionUtil.hashMap("merchant_id", merchantId));
            if (Objects.isNull(listResult) || CollectionUtils.isEmpty(listResult.getRecords())) {
                log.warn("获取商户银行卡信息为空, merchantSn={}, merchantId={}", merchantSn, merchantId);
                return null;
            }
            
            List<Map> records = listResult.getRecords();
            // oldAcctNo的格式是：621700*********8167，需要在records中找到number和oldAcctNo匹配的记录
            Map<String, Object> matchedOldAccount = findMatchingBankAccount(records, oldAcctNo);
            
            BankAccountSimpleInfoBO oldBankAccount = buildBankAccountInfo(matchedOldAccount);
            
            // 找不到匹配银行卡的情况
            if (oldBankAccount == null) {
                log.warn("Cannot find matching bank account for masked card number, merchantSn={}, " +
                        "maskedCardNo={}, availableCardsCount={}", 
                        merchantSn, oldAcctNo, records.size());
            }
            return oldBankAccount;
            
        } catch (Exception e) {
            log.error("Get LklV3 old account info failed, merchantSn={}", merchantSn, e);
            return null;
        }
    }

    /**
     * 创建轮询任务
     */
    private void createPollingTask(String merchantSn, Long contractTaskId, Long contractSubTaskId, String acquirer,
                                   String  providerMerchantId,
                                 BankAccountSimpleInfoBO oldBankAccount,
                                 BankAccountSimpleInfoBO newBankAccount) {
        try {

            // 构建轮询任务上下文
            RotationalTaskContext taskContext = AcquirerAccountChangePollingTaskBuilder.create()
                    .merchantSn(merchantSn)
                    .contractTaskId(contractTaskId)
                    .contractSubTaskId(contractSubTaskId)
                    .acquirer(acquirer)
                    .acquirerMerchantId(providerMerchantId)
                    .belongToContractTask(false)
                    .oldAccountInfo(oldBankAccount)
                    .newAccountInfo(newBankAccount)
                    .remark(acquirer + "收单机构换卡结果轮询")
                    .buildContext();
            // 创建轮询任务
            rotationalTask.buildTaskForContractTask(taskContext);
            
            log.info("Created polling task for account change, merchantSn={}, acquirer={}, " +
                    "contractTaskId={}, contractSubTaskId={}, rotationId={}", 
                    merchantSn, acquirer, contractTaskId, contractSubTaskId,
                    taskContext.getRotationId());
                    
        } catch (Exception e) {
            log.error("Create polling task failed, merchantSn={}, acquirer={}, contractTaskId={}, contractSubTaskId={}", 
                    merchantSn, acquirer,
                    contractTaskId, contractSubTaskId, e);
        }
    }

    /**
     * 查找匹配的银行账户记录
     */
    private Map<String, Object> findMatchingBankAccount(List<Map> records, String maskedCardNo) {
        if (StringUtils.isBlank(maskedCardNo)) {
            log.warn("Masked card number is blank, cannot find matching account");
            return null;
        }
        
        if (CollectionUtils.isEmpty(records)) {
            log.warn("Bank account records list is empty, cannot find matching account for maskedCardNo={}", 
                    maskedCardNo);
            return null;
        }
        
        List<Map<String, Object>> matchedAccounts = new ArrayList<>();
        List<String> availableCardNumbers = new ArrayList<>();
        
        for (Map record : records) {
            String cardNumber = MapUtils.getString(record, "number");
            if (StringUtils.isNotBlank(cardNumber)) {
                availableCardNumbers.add(cardNumber);
                
                if (lklV3MerchantInfoProcessor.compareBankCardNos(cardNumber, maskedCardNo)) {
                    matchedAccounts.add(record);

                }
            }
        }
        
        if (matchedAccounts.isEmpty()) {
            log.warn("No matching bank account found for maskedCardNo={}, available cards: [{}]", 
                    maskedCardNo, String.join(", ", availableCardNumbers));
            return null;
        } else if (matchedAccounts.size() == 1) {
            log.debug("Found exactly one matching bank account for maskedCardNo={}", maskedCardNo);
            return matchedAccounts.get(0);
        } else {
            log.warn("Found multiple matching bank accounts for maskedCardNo={}, count={}, using first match", 
                    maskedCardNo, matchedAccounts.size());
            return matchedAccounts.get(0);
        }
    }

    /**
     * 构建BankAccountCheckInfo对象
     */
    private BankAccountSimpleInfoBO buildBankAccountInfo(Map accountMap) {
        if (MapUtils.isEmpty(accountMap)) {
            return null;
        }
        BankAccountSimpleInfoBO bankAccount = new BankAccountSimpleInfoBO();
        bankAccount.setCardNumber(MapUtils.getString(accountMap, MerchantBankAccount.NUMBER));
        bankAccount.setType(MapUtils.getInteger(accountMap, MerchantBankAccount.TYPE));
        bankAccount.setHolder(MapUtils.getString(accountMap, MerchantBankAccount.HOLDER));
        bankAccount.setHolderCertificateNumber(MapUtils.getString(accountMap, MerchantBankAccount.IDENTITY));
        return bankAccount;
    }

}
