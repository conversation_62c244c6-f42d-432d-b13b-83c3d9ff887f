jsonrpc.merchant_contract=http://merchant-contract.beta.iwosai.com/
jsonrpc.core_business=http://core-business.beta.iwosai.com/
jsonrpc.notice_service=http://notice-service.beta.iwosai.com/
jsonrpc.app_push_service=http://app-push-service.beta.iwosai.com/
jsonrpc.app_backend_service=http://app-backend-process-service.beta.iwosai.com/
jsonrpc.business_log=http://business-log.beta.iwosai.com/
jsonrpc.business_log_stash.server=http://business-logstash.beta.iwosai.com
jsonrpc.opr_merchant_activity=http://opr-merchant-activity.beta.iwosai.com/
jsonrpc.shouqianba_merchant.server=http://merchant.beta.iwosai.com/
jsonrpc.crm=http://sales-system-service.beta.iwosai.com/
jsonrpc.sales-system-poi=http://sales-system-poi.beta.iwosai.com/
jsonrpc.remit.order.server=http://remit-gateway.beta.iwosai.com/
jsonrpc.upay.side.server=http://upay-side.beta.iwosai.com/
jsonrpc.bank-info.server=http://bank-info-service.beta.iwosai.com/
jsonrpc.merchant-bank.server=http://merchant-bank-service.beta.iwosai.com/
jsonrpc.merchant-audit.server=http://merchant-audit-service.beta.iwosai.com/
jsonrpc.merchant-enrolment.server=http://merchant-enrolment.beta.iwosai.com/
jsonrpc.risk-disposal.server=http://risk-disposal.beta.iwosai.com
jsonrpc.trade_manage=http://trade-manage-service.beta.iwosai.com
jsonrpc.upay-wallet=http://upay-wallet.beta.iwosai.com
jsonrpc.withdraw-service=http://shouqianba-withdraw-service.beta.iwosai.com
jsonrpc.finance-backend=http://finance-backend.beta.iwosai.com
jsonrpc.alipay=http://alipay-authinto.beta.iwosai.com
jsonrpc.profit-sharing=http://profit-sharing.beta.iwosai.com
jsonrpc.merchant_user_service=http://merchant-user-service.beta.iwosai.com
jsonrpc.aop_gate=http://aop-gateway.beta.iwosai.com
jsonrpc.sp-workflow-service=http://sp-workflow-service.beta.iwosai.com
jsonrpc.merchant-center=http://merchant-center.beta.iwosai.com
jsonrpc.crow=http://***************:18081
jsonrpc.qrcode=http://upay-qrcode.beta.iwosai.com
jsonrpc.crm-customer-relation=http://crm-customer-relation.beta.iwosai.com
jsonrpc.upay-transaction=http://upay-transaction.beta.iwosai.com
jsonrpc.pay-business-open=http://pay-business-open.beta.iwosai.com
jsonrpc.core-crypto=http://core-crypto.beta.iwosai.com
jsonprc.authcode-service=http://authcode.beta.iwosai.com
jsonrpc.sp-task=http://sp-task.beta.iwosai.com
jsonrpc.contract-activity=http://merchant-contract-activity.beta.iwosai.com
jsonrpc.merchant_level=http://merchant-level.beta.iwosai.com
jsonrpc.boss-circle-user=http://boss-circle-user.beta.iwosai.com
jsonrpc.agreement-manage=http://agreement-manage.beta.iwosai.com
jsonrpc.merchant-business-open = http://merchant-business-open.beta.iwosai.com
jsonrpc.campus-center=http://campus-center.beta.iwosai.com
jsonrpc.credit-pay-backend= http://credit-pay-backend.beta.iwosai.com
jsonrpc.sales-system-backend=http://sales-system-backend.beta.iwosai.com
jsonrpc.crm-todo-api=http://crm-todo
jsonrpc.risk-service=http://shouqianba-risk-service.beta.iwosai.com
jsonrpc.merchant-contract-access=http://merchant-contract-access.beta.iwosai.com
jsonrpc.brand-business=http://brand-business.beta.iwosai.com
jsonrpc.partner-payment-hub: http://partner-payment-hub.beta.iwosai.com
jsonrpc.acquirer-notification-service=http://acquirer-notification-service.beta.iwosai.com
jsonrpc.lakala-proxy=http://lakala-proxy.beta.iwosai.com

jsonrpc.dc-service=http://dc-service.beta.iwosai.com
jsonrpc.jupiter=http://jupiter.beta.iwosai.com
jsonrpc.upay-grayscale=http://upay-grayscale.beta.iwosai.com

##连接池配置
spring.datasource.url=tk_merchant-contract-job-merchant_contract-8104
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=30
spring.datasource.hikari.connection-test-query=select 1 from dual
#富友提交对公凭证审批模板id
fuyou.approval.templateId=194117
spring.application.name=merchant-contract-job
app.h5_url=http://pay-after.test.shouqianba.com/#/status
contract.lakala.upload.time=3000
contract.lakala.task.query.time=60000
#银联开放平台轮询查询时间
contract.union.open.query.time=180000
# redis beta
spring.redis.database=2
spring.redis.host=r-8vbsiayrjo64l0t62e.redis.zhangbei.rds.aliyuncs.com
spring.redis.port=6379
spring.redis.password=bRUY^ORbRdNBIR#FgGPn!9Ao32qU
spring.redis.pool.max-active=8
spring.redis.pool.max-wait=-1
spring.redis.pool.max-idle=8
spring.redis.pool.min-idle=0
spring.redis.timeout=30000
contract.type.lakala=WSST
#kafka相关配置
bank.message.send.topic=events.upay.merchant-contract-bank-status
spring.kafka.bootstrap-servers=**************:9092,**************:9092,**************:9092
spring.kafka.registry-servers=http://**************:8081,http://**************:8081,http://**************:8081
#设置一个默认组
spring.kafka.consumer.group-id=merchant-contract-job
spring.kafka.producer.group-id=merchant-contract-job
spring.kafka.topics.merchant-message=events.upay.merchant-contract-job.merchant-message
shence.topic.wxApply=events.merchant-contract-job.wx_apply
sensor.topic.bind_bank=events.upay.merchant-bind-bank
#阿里kafka相关配置
spring.kafka.ali.bootstrap-servers=aliyun-beta-kafka-01.iwosai.com:9092,aliyun-beta-kafka-02.iwosai.com:9092,aliyun-beta-kafka-03.iwosai.com:9092
spring.kafka.ali.registry-servers=http://aliyun-beta-schema-01.iwosai.com:8081,http://aliyun-beta-schema-02.iwosai.com:8081,http://aliyun-beta-schema-03.iwosai.com:8081
spring.kafka.ali.consumer.group-id=merchant-contract-job
#间连扫码进件
sensor.topic.contract_action=events.merchant-contract-job.contract-action
#dts.params中 '|'为完整dts订阅通道参数分隔符
dts.params=test_data_sync,LTAIILAOJ1Zcd9df,SSM9OVuyiGWGYnhLazh0YJR87l6ODr,dtss4ru6uu02iic
dts.init=false
mockdts.init=false
schedule.init=false
config.init=false
#代付消息发送仅测试环境
payfortest.init=false
spring.rabbitmq.host=***********
spring.rabbitmq.port=5672
spring.rabbitmq.username=guest
spring.rabbitmq.password=wosai1234
databus.consumer.topic=databus_CUA_merchant_basic_allin,databus_PAY_merchant_config_allin
databus.consumer.store.topic=databus_CUA_store_basic_allin
databus.consumer.terminal.topic=databus_CUA_terminal_basic_allin
#微信二维码生成的文件目录
wechat_applet_code.build.path=./
weixin.apply.query.time=5000
weixin.auth.query.today.time=5000
lkl.channelno=WSST
lkl.v3.channelno=228783
tl.channelno=7894
tl.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDYXfu4b7xgDSmEGQpQ8Sn3RzFgl5CE4gL4TbYrND4FtCYOrvbgLijkdFgIrVVWi2hUW4K0PwBsmlYhXcbR+JSmqv9zviVXZiym0lK3glJGVCN86r9EPvNTusZZPm40TOEKMVENSYaUjCxZ7JzeZDfQ4WCeQQr2xirqn6LdJjpZ5wIDAQAB

acquirer-change-day-cron=8/10 * * * * ?
feerate-activity-cron=1/5 * * * * ?
indirect-pay.dev_code=PQPBJGNJC26N
#微信直连相关的配置
weixin.direct.online=LTVAJRCGMX59
weixin.direct.offline=PJOWY29OWSZI
weixin.direct.apply.query.time=5000
#支付宝直连相关的配置
ali.direct=MMNMVMGFWSJD
direct.apply.topic=events.merchant-contract-job.direct.apply

#接收阿里推送消息
ant.target.serve=
mail-gateway=http://email-gateway.beta.iwosai.com/test

BLUESEA_TAG=e72e7547-1918-4849-b06d-81e6f5c74a21
KX_TAG=6c465424-d891-46c3-ae53-78cd5b373137
tradeAppId=2
#线下教培邮件发送时间
offline_edu_train=0 0/3 * * * ?
#Ep101通知标识
clientSide.notice.devCode=PQPBJGNJC26N
#小微商户升级crm通知
clientSide.crm.devCode=PQPBJGNJC26N
#小微商户升级app通知
clientSide.app.devCode=PQPBJGNJC26N
#小微商户升级crm通知
clientSide.crm.templateCode=PH2MPXCILHLZ
#小微商户升级失败crm通知
clientSide.crm.failTemplateCode=FJZRU4QVBPQM
#小微商户升级app通知
clientSide.app.templateCode=XOCVNHTCFCON

#银商定时查询时间
contract.ums.task.query.time=30000

#邮储签约通知code
psbc_template_code=D9BQGNTI7892
#银行合作开发通知code
psbc_bank_notice_dev_code=HSNRPQMTVK8Q
#邮储业务标识
psbc_business_dev_code=PA5CUQYDF39A
#银行直连微信商家认证查询频率
process_status_wxAuth=1/5 * * * * ?
#银行创建切换任务查询频率
process_bank_changeAcquire=60000
#实名成功切换收单机构
process_status_changeAcquire=1/5 * * * * ?
#广发
cgb_dev_code=8U8YXCFF8J5U


#高校食堂标签Id
schoolcanteen_tag=c0366892-4075-4afb-88ce-0e121dd3c0be
#蚂蚁门店查询时间
ant.shop.query.time=40000

#切换收单机构银行卡前置校验延长时间
bank_delay_minute_time=2

#邮储通知crm协助商户完成实名,模板code
psbc_crm_template_code = APSFDEZRKSHF
#邮储通知crm协助商户完成实名,开发标识
psbc_crm_dev_code = SQLKC6OKJK77

#邮储通知商户完成实名,模板code
psbc_app_template_code = IQDV3GBNXZWL
#邮储通知商户完成实名,开发标识
psbc_app_dev_code = HSNRPQMTVK8Q

#银商入网中 切换 拉卡拉
contract.ums.event.ing.time=40000

#建行应用标识
ccb_dev_code=QV8FBQRYA0FA
#建行通知商户app签约模版code
ccb_sign_template_code=HMLQEF5PDMDU
#建行通知crm协助商户完成实名,模板code和开发code
ccb_crm_template_code=APSFDEZRKSHF
ccb_crm_dev_code=SQLKC6OKJK77
#建行通知app商户完成实名,模板code和开发code
ccb_app_template_code=IQDV3GBNXZWL
ccb_app_dev_code=QV8FBQRYA0FA
indirect_crm_customer_relation=merchantorg
kabu_organization_path=00069
query_ccb_contract_status_cron=2/30 * * * * ?
#维护人code
maintain.devCode=test-bd-merchant
#小微营业执照升级模板
upgrade.applyId=205100
#小微营业执照升级模板
upgrade.authApplyId=205100

#客服作业平台进入详情地址
customer_platform_address_url1=https://sp-upay-api.beta.iwosai.com/subtaskStatus/getLacarraSubtaskStatus?id=%s&merchant_sn=%s
customer_platform_address_url2=https://sp-upay-api.beta.iwosai.com/subtaskStatus/getWeChatAndAlipaySubtaskStatus?id=%s&merchant_sn=%s&p_task_id=%s

#app开通数字钱包跳转地址
ccb_decp_wait_open=https://side-page.shouqianba.com/side-page-beta/CCBdigitalmoneyOpen/index.html?env=beta&token=:token
ccb_decp_process_open=https://side-page.shouqianba.com/side-page-beta/CCBdigitalmoneyOpening.html
ccb_decp_success_open=https://side-page.shouqianba.com/side-page-beta/CCBdigitalmoneySuccess/index.html?env=beta&token=:token
ccb_decp_fail_open=https://side-page.shouqianba.com/side-page-beta/CCBdigitalmoneyFail.html?env=beta&token=:token
merchant-contract.access_id=50b2a928-55cc-4839-9a70-e33cbd904f23
merchant-contract.access_secret=c9b7607093e249eb8c77632152670172
ccb_decp.template_id=ELXR1VDVRJ10
ccb_decp_success_notice_code=VSEWINDHQVUM
ccb_decp_success_push_code=VU078SDJQQX2
#银行直连报表统计邮件发送时间
send_success_mail=2/50 * * * * ?
#银行活动商户微信实名认证派工
wx_task_template_id=40489
#银行活动商户协议签约派工
sign_task_template_id=40490

#华夏应用标识
hxb_dev_code=SGJWMX7JFINP
#华夏通知crm协助商户完成实名,模板code和开发code
hxb_crm_template_code=YM5ZXKDOGTCG
hxb_crm_dev_code=SGJWMX7JFINP
#华夏通知收钱吧App协助商户完成实名,模板code和开发code
hxb_app_template_code=H7XEDWO6MARZ
hxb_app_dev_code=SGJWMX7JFINP
#工商银行应用标识
icbc_dev_code=IF5FDZN3HZCZ
#民生银行应用标识
cmbc_dev_code=GYESQTY4JFXP
#民生银行套餐ID
cmbc_combo_id = 20475
#平安银行应用标识
pab_dev_code=G1ACMVIK2VAN
pab_trade_combo_id=19071
#平安签约通知code
pab_template_code=EVK7K0QU1KSQ
#平安签约dev_code
pab_sign_dev_code=LUY3ZYMIHNVP
#平安微信实名app通知模板
pab_app_template_code=FOLSPGWIKCPX
#平安微信实名app通知业务标识
pab_app_dev_code=LUY3ZYMIHNVP

#支付网关预下单
upay_gateway_precreate=http://upay-gateway.beta.iwosai.com/upay/v2/precreate
hxb_check_tag_1=9db021b2-0b8c-4870-ada4-ddb21a86cb9f
#hxb_check_value_1=市场额度包
crow.merchant_entry_id=af1de674-2cf9-4491-8c65-7a649d3fada4

#crm应用标识
hxb_app_id=9c4119fd-7e03-4413-aa9f-a5daa0dade3f
icbc_app_id=a5d71420-6c20-4020-906a-579dcb9745e5
pab_app_id=3591fcdf-84eb-4a40-a682-7085462376cf
#建行批量导入
ccb_trade_combo_id=794
ccb_tag=b46a8673-c6ca-44f3-8d0e-b96b4600e907
ccb_app_id=0a261857-5a1f-41a2-a599-8fa40f91cf51
ccb_user_id=2891226a-297f-4105-b601-cd6560fc8218

#消息消费开关
consumer.init = false

# redisson
redisson.redis.host=r-8vbsiayrjo64l0t62e.redis.zhangbei.rds.aliyuncs.com
redisson.redis.port=6379
redisson.redis.database=3
redisson.redis.password=bRUY^ORbRdNBIR#FgGPn!9Ao32qU

logging.level.com.wosai.upay.job=debug
#华夏银行多业务套餐
hxb_multi_trade=11895

#先享后付通知配置
pay_later.combId=12770
pay_later.devCode=PQPBJGNJC26N
pay_later.zftTemplateCode=AACBH6XMDXQK
pay_later.zhiMaTemplateCode=9JGPUY4ARTE2
#直付通状态查询
queryZftApply=0 0/1 * * * ?
#火山相关配置
spring.kafka.bootstrap-servers-data-center=aliyun-beta-kafka-01.iwosai.com:9092,aliyun-beta-kafka-02.iwosai.com:9092,aliyun-beta-kafka-03.iwosai.com:9092
topic.kafka.data-center=analytics_data_volcengine_push

#芝麻商户审核是否超时
zhiMa_merchant_timeout=60000

lkl_pso_dev_code=GF7GNKL9O48N
fy_pso_dev_code=EK6NH2M5ZFQA

bank_error_change_to_third_task_template_id=253720
bank.cooperation.notice.app.devCode=NFXRFW1XCD9Y
bank.cooperation.notice.crm.devCode=NZT0IL3UJXQY
bank.auto.change.third.notify.template_id=J8XN1YMCBMHG
third.auto.change.bank.notify.devCode=NFXRFW1XCD9Y
third.auto.change.bank.notify.template_id=87J0QL7KGQXZ
close.trade.protection.notify.template_id=IZQ6HD83EKFO
open.trade.protection.notify.template_id=LI78CK1OYOGH
bank.trade.protection.close.template_id=X4VMQG6VUVTG
bank.trade.protection.not.close.template_id=E0RG1I9FNRLZ
bank.trade.protection.recover.template_id=UOKZRXDRUIXB
bank.trade.protection.not.recover.template_id=2RBDHUXTQOFY
bank.trade.protection.log.template.code=6QVNUUVO4N2V


#富友一体化外卡
fyForeignCard.devCode = EDNGVEMIL2SI
#拉卡拉一体化外卡
lklForeignCard.devCode = OYLIAHFL3SEK
#拉卡拉手机pos外卡
lklMobilePos.devCode = FEKUON3QNHPJ
#拉卡拉手机pos业务在crm的应用主键
lklMobilePos.appId = 92a6116f-9201-40ff-8592-************ 
lkl_open.contract_rule=lkl_open-1034-17-228783



##对接错误码管理平台
spring.scene-manage-service.url: http://scene-manage-service.beta.iwosai.com/
spring.scene-manage-service.project_name: merchant-contract
spring.scene-manage-service.synonym_map:{"payway":[[1,2],[3,4,7]]} 

zjtlcb_trade_combo_id=19106

#京东钱包套餐
jd.combId=17902

#b2b的appid
b2b.appid=10287

#b2b活动费率套餐的模板
trade.comboId=21644

#标准活动费率套餐的模板
trade.baseComboId=22306

wx_complaint_task_template_id=294862

tag.auth.layer.wx.contract=8e88d445-c8e7-4f62-b9e3-8061fa5240fb
tag.auth.layer.ali.contract=8f5951ba-5909-48be-ade9-f807fc6bf713

#通道预授权
lkl.preAuth.devCode = lkl
fy.preAuth.devCode = IGS3ZUGTRHNB
#应用ID
fy.preAuth.appId = ac8a4c66-d675-451e-a949-3c07d2b4af42

#拉卡拉一体化刷卡待办跳转
lkl.pos.todo.appUrl=https://device-crm-web.iwosai.com/bank-card?
#拉卡拉一体化刷卡appid
lkl.pos.appId=9ba82122-6b79-469c-a5a7-406c7813fc43
#拉卡拉一体化外卡待办跳转
lkl.foreign.todo.appUrl=https://device-crm-web.iwosai.com/foreign-card?
#拉卡拉一体化外卡appid
lkl.foreign.appId=ee15c2fd-caea-4149-bd88-ddcdc7faa885


## 业务方名称和业务方应用ID的对应关系
# 扫码点单
scan_order=6
# 支付业务
payment=1
# 刷卡收款
card_payment=5
# 银行合作
bank_cooperation=3
# 线上
online=7083
# 跨城收款
cross_city_payment=7084
# 手机POS
mobile_pos=8222
# 校园外卖
campus_food_delivery=4

##

# 通联刷卡
tl_pos_devCode= FJYKBDP5YLFQ
#通联一体化外卡
tl_foreignCard_devCode = ULO2JKRDDUHA
#通联预授权
tl_preAuth_devCode = MFAD2VNCGWT2
#通联预授权appid
tl.preAuth.appId = 8a52d0f4-3fe8-44be-87c6-4212626e6dd2

bcs_dev_code=NDDG9DXZ9W0P
bcs_trade_combo_id=24744
bcs_sm2_private_key=5cf391d0229fe420ab0718d76376f87269358c66dfcc3b505ed00458a84590f4
bcs_sm2_public_key=04EAD91511D98791D83A35434BF7D061776CF2CB04AFA300382F80CE4718BC388D8FCA43C05DD4D685E634CCB44631A37BFF05D83BB7A32D127277990CB7706E9B
bcs_app_template_code=ZFMHRGSLD0JK